'use client';
import React from 'react';
import { Accordion, ActionIcon, Button, Card, Grid, Input, NumberInput, Select, Text, Textarea } from '@mantine/core';
import { modals } from '@mantine/modals';
import { IconCancel, IconCheck, IconEdit, IconMessageLanguage, IconPlus, IconTrash } from '@tabler/icons-react';
import { convertToFormData, handleFetchSubmit } from '@/common/functions/formFunctions';

const getSentimentEmoji = (sentimentScore) => {
  if (sentimentScore === null || sentimentScore === undefined) return '';
  const score = Math.round(sentimentScore);
  if (score < 50) return '😞';
  if (score === 50) return '😐';
  if (score > 50 && score <= 70) return '😊';
  if (score > 70) return '😄';
  return '';
};

const formatTime = (timeInSeconds) => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`;
};

const normalizeText = (text) => {
  if (typeof text !== 'string') return '';
  return text
    .replace(/İ/g, 'i')
    .replace(/I/g, 'i')
    .replace(/ı/g, 'i')
    .replace(/Ö/g, 'o')
    .replace(/ö/g, 'o')
    .replace(/Ü/g, 'u')
    .replace(/ü/g, 'u')
    .replace(/Ş/g, 's')
    .replace(/ş/g, 's')
    .replace(/Ğ/g, 'g')
    .replace(/ğ/g, 'g')
    .replace(/Ç/g, 'c')
    .replace(/ç/g, 'c')
    .toLowerCase()
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '');
};

const Highlight = ({ text, keywords }) => {
  if (!text || !Array.isArray(keywords) || keywords.length === 0) return text;
  const normalizedText = normalizeText(text);
  const matches = [];
  keywords
    .map((word) => normalizeText(word))
    .filter((word) => word.length > 0)
    .forEach((normWord) => {
      let startIndex = 0;
      while (startIndex < normalizedText.length) {
        const index = normalizedText.indexOf(normWord, startIndex);
        if (index === -1) break;
        matches.push({ start: index, end: index + normWord.length });
        startIndex = index + normWord.length;
      }
    });
  const merged = [];
  matches
    .sort((a, b) => a.start - b.start)
    .forEach((match) => {
      const last = merged[merged.length - 1];
      if (last && match.start <= last.end) {
        last.end = Math.max(last.end, match.end);
      } else {
        merged.push({ ...match });
      }
    });
  const result = [];
  let lastIndex = 0;
  merged.forEach(({ start, end }, i) => {
    if (lastIndex < start) {
      result.push(text.slice(lastIndex, start));
    }
    result.push(
      <span key={i} style={{ backgroundColor: 'yellow' }}>
        {text.slice(start, end)}
      </span>
    );
    lastIndex = end;
  });
  if (lastIndex < text.length) {
    result.push(text.slice(lastIndex));
  }
  return <>{result}</>;
};

export function ChatWindow({
  channelType,
  detail,
  permissions,
  id,
  selectedLanguage,
  setSelectedLanguage,
  searchTerm,
  setSearchTerm,
  currentSentence,
  currentTime,
  currentMessageModified,
  setCurrentMessageModified,
  analysisResultId,
  audioPlayerRef,
  fetchAuthClient,
  setDetail,
}) {
  return (
    <>
      {channelType === 'Call' && detail.dto.info && (
        <>
          <div style={{ display: 'flex' }}>
            {permissions.includes('Call.UpdateSentence') && (
              <Button
                me="md"
                onClick={async () => {
                  handleFetchSubmit(
                    fetchAuthClient('CallSentence/add/' + id + '/' + selectedLanguage + '/' + analysisResultId, {
                      method: 'GET',
                    }),
                    async (response) => {
                      const responseJson = await response.json();
                      let filtered = detail.sentences;
                      filtered.push(responseJson);
                      filtered.sort((a, b) => a.start - b.start);
                      setDetail((prev) => ({
                        ...prev,
                        sentences: filtered,
                      }));
                      setCurrentMessageModified(null);
                    }
                  );
                }}
              >
                <IconPlus />
              </Button>
            )}
            <div style={{ position: 'relative', width: '100%', marginBottom: '16px' }}>
              <Input
                style={{ width: '100%' }}
                placeholder="Konuşmada ara..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                }}
              />
              {searchTerm && (
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  onClick={() => setSearchTerm('')}
                  size="sm"
                  style={{
                    position: 'absolute',
                    right: '8px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    cursor: 'pointer',
                    zIndex: 10,
                    backgroundColor: 'white',
                  }}
                >
                  <IconCancel size={16} />
                </ActionIcon>
              )}
            </div>
            {detail.dto.language === 'tr' ? (
              <></>
            ) : (
              <>
                <Button
                  onClick={async () => {
                    if (selectedLanguage === detail.dto.language) {
                      setSelectedLanguage('tr');
                    } else {
                      setSelectedLanguage(detail.dto.language);
                    }
                  }}
                >
                  {selectedLanguage}
                  <IconMessageLanguage />
                </Button>
              </>
            )}
          </div>
          <Grid gutter="md" style={{ maxHeight: '500px', overflowX: 'hidden', overflowY: 'scroll' }}>
            {(selectedLanguage === detail.dto.language ? detail.sentences : detail.convertedSentences)
              .filter((sentence) =>
                normalizeText(searchTerm)
                  .split(';')
                  .some((y) => normalizeText(sentence.text).includes(normalizeText(y)))
              )
              .map((sentence, index) => (
                <Grid.Col span={12} key={index}>
                  <Card
                    withBorder
                    shadow="md"
                    padding="md"
                    className="cursor-pointer"
                    style={{
                      width: '80%',
                      float: sentence.isAgent ? 'left' : 'right',
                      clear: sentence.isAgent ? 'left' : 'right',
                      background: currentSentence
                        ? currentTime >= sentence.start &&
                          currentTime <= sentence.end &&
                          sentence.text === currentSentence.text
                          ? 'lime'
                          : ''
                        : '',
                    }}
                  >
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <b
                        className="me-3 text-small"
                        style={{ textAlign: 'justify', marginLeft: sentence.isAgent ? '0px' : 'auto' }}
                      >
                        {currentMessageModified && currentMessageModified.id === sentence.id ? (
                          <>
                            <ActionIcon
                              variant="subtle"
                              color="teal"
                              onClick={async () => {
                                handleFetchSubmit(
                                  fetchAuthClient('CallSentence/update', {
                                    method: 'POST',
                                    body: convertToFormData({
                                      id: currentMessageModified.id,
                                      text: currentMessageModified.text,
                                      start: parseInt(currentMessageModified.start),
                                      end: parseInt(currentMessageModified.end),
                                      isAgent: currentMessageModified.isAgent,
                                    }),
                                  }),
                                  async (response) => {
                                    let filtered = detail.sentences.filter((x) => x.id !== currentMessageModified.id);
                                    filtered.push(currentMessageModified);
                                    filtered.sort((a, b) => a.start - b.start);
                                    setDetail((prev) => ({
                                      ...prev,
                                      sentences: filtered,
                                    }));
                                    setCurrentMessageModified(null);
                                  }
                                );
                              }}
                            >
                              <IconCheck style={{ width: '70%', height: '70%' }} stroke={1.5} />
                            </ActionIcon>
                            <ActionIcon
                              color="red"
                              variant="subtle"
                              onClick={() => {
                                setCurrentMessageModified(null);
                              }}
                            >
                              <IconCancel style={{ width: '70%', height: '70%' }} stroke={1.5} />
                            </ActionIcon>
                            <div style={{ display: 'flex' }}>
                              <Select
                                data={['TEMSİLCİ', 'MÜŞTERİ']}
                                value={currentMessageModified.isAgent ? 'TEMSİLCİ' : 'MÜŞTERİ'}
                                onChange={(data) => {
                                  setCurrentMessageModified((prev) => ({
                                    ...prev,
                                    isAgent: data === 'TEMSİLCİ',
                                  }));
                                }}
                              />
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                }}
                              >
                                <NumberInput
                                  ms={'md'}
                                  step={1}
                                  min={0}
                                  value={parseInt(currentMessageModified.start)}
                                  onChange={(data) => {
                                    setCurrentMessageModified((prev) => ({
                                      ...prev,
                                      start: parseInt(data),
                                    }));
                                  }}
                                />
                                <small
                                  style={{ color: 'blue' }}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setCurrentMessageModified((prev) => ({
                                      ...prev,
                                      start: parseInt(currentTime),
                                    }));
                                  }}
                                >
                                  Mevcut Zaman {parseInt(currentTime)}
                                </small>
                              </div>
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                }}
                              >
                                <NumberInput
                                  ms={'md'}
                                  step={1}
                                  min={0}
                                  value={parseInt(currentMessageModified.end)}
                                  onChange={(data) => {
                                    setCurrentMessageModified((prev) => ({
                                      ...prev,
                                      end: parseInt(data),
                                    }));
                                  }}
                                />
                                <small
                                  style={{
                                    color: 'blue',
                                    textAlign: 'center',
                                  }}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setCurrentMessageModified((prev) => ({
                                      ...prev,
                                      end: parseInt(currentTime),
                                    }));
                                  }}
                                >
                                  Mevcut Zaman {parseInt(currentTime)}
                                </small>
                              </div>
                            </div>
                            <Textarea
                              resize="vertical"
                              mt={'md'}
                              value={currentMessageModified ? currentMessageModified.text : ''}
                              onChange={(event) => {
                                if (event && event.currentTarget && event.currentTarget.value) {
                                  let newValue = event.currentTarget.value;
                                  setCurrentMessageModified((prev) => ({
                                    ...prev,
                                    text: newValue,
                                  }));
                                }
                              }}
                            />
                          </>
                        ) : (
                          <>
                            {permissions.includes('Call.UpdateSentence') && (
                              <>
                                <ActionIcon
                                  variant="subtle"
                                  color="gray"
                                  onClick={() => {
                                    setCurrentMessageModified(sentence);
                                  }}
                                >
                                  <IconEdit style={{ width: '70%', height: '70%' }} stroke={1.5} />
                                </ActionIcon>
                                <ActionIcon
                                  variant="subtle"
                                  color="gray"
                                  onClick={async () => {
                                    modals.openConfirmModal({
                                      title: 'İşlem Onayı',
                                      children: (
                                        <span>Bu işlem geri alınamaz, devam etmek istediğinize emin misiniz?</span>
                                      ),
                                      labels: {
                                        confirm: 'Devam Et',
                                        cancel: 'İptal',
                                      },
                                      onConfirm: () => {
                                        handleFetchSubmit(
                                          fetchAuthClient('CallSentence/delete/' + sentence.id, {
                                            method: 'GET',
                                          }),
                                          async (response) => {
                                            let filtered = detail.sentences.filter((x) => x.id !== sentence.id);
                                            filtered.sort((a, b) => a.start - b.start);
                                            setDetail((prev) => ({
                                              ...prev,
                                              sentences: filtered,
                                            }));
                                          }
                                        );
                                      },
                                    });
                                  }}
                                >
                                  <IconTrash style={{ width: '70%', height: '70%' }} stroke={1.5} />
                                </ActionIcon>
                              </>
                            )}
                            {sentence.isAgent ? 'TEMSİLCİ' : 'MÜŞTERİ'}{' '}
                            {' (' + formatTime(sentence.start) + '-' + formatTime(sentence.end) + ')'}
                            <span style={{ marginLeft: '8px' }}>{getSentimentEmoji(sentence.sentiment)}</span>
                          </>
                        )}
                      </b>
                      {currentMessageModified && currentMessageModified.id === sentence.id ? (
                        <></>
                      ) : (
                        <p
                          onClick={() => audioPlayerRef.current.handleResultClick(sentence.start, sentence.end)}
                          className={'text-small ' + (sentence.isAgent ? 'text-start' : 'text-end')}
                          style={{
                            margin: '0px',
                            textAlign: sentence.isAgent ? 'justify' : 'right',
                          }}
                        >
                          <Highlight text={sentence.text} keywords={searchTerm.split(';')} />
                        </p>
                      )}
                    </div>
                  </Card>
                </Grid.Col>
              ))}
          </Grid>
        </>
      )}
      {channelType === 'Chat' && (
        <>
          <div style={{ position: 'relative', width: '100%', marginBottom: '16px' }}>
            <Input
              style={{ width: '100%' }}
              placeholder="Konuşmada ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={() => setSearchTerm('')}
                size="sm"
                style={{
                  position: 'absolute',
                  right: '8px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  cursor: 'pointer',
                  zIndex: 10,
                  backgroundColor: 'white',
                }}
              >
                <IconCancel size={16} />
              </ActionIcon>
            )}
          </div>
          {(() => {
            let messages = searchTerm
              ? detail.sentences.filter(
                  (sentence) =>
                    sentence.messageType == 'TEXT' &&
                    normalizeText(searchTerm)
                      .split(';')
                      .some((y) => normalizeText(sentence.content).includes(normalizeText(y)))
                )
              : detail.sentences;
            const agentJoinIndex = messages.findIndex(
              (sentence) => sentence.messageType === 'COMMAND' && sentence.commandType === 'INTERNAL_HANDOVER'
            );
            const messagesBeforeAgent = agentJoinIndex !== -1 ? messages.slice(0, agentJoinIndex) : messages;
            const messagesAfterAgent = agentJoinIndex !== -1 ? messages.slice(agentJoinIndex) : [];

            const renderContent = (sentence) => {
              const { messageType, content, commandType } = sentence;

              if (messageType === 'TEXT') {
                return <Highlight text={content} keywords={searchTerm.split(';')} />;
              }

              if (messageType === 'CARD') {
                return JSON.parse(content).map((h, hIndex) => (
                  <div key={hIndex}>
                    <Text weight={500} size="lg" mb="sm">
                      {h.title}
                    </Text>
                    <Text mb="sm">{h.text}</Text>
                    {h.image ? (
                      <>
                        {h.subTitle}
                        <br />
                        <img width={128} src={h.image} alt="" />
                      </>
                    ) : (
                      h.buttons.map((btn, btnIndex) => (
                        <Button key={btnIndex} style={{ width: '100%', marginBottom: 4 }}>
                          {btn.text}
                        </Button>
                      ))
                    )}
                  </div>
                ));
              }

              if (messageType === 'LIST') {
                return JSON.parse(content).listSections.map((section, sIndex) => (
                  <div key={sIndex}>
                    {section.listCardRows?.map((row, rIndex) => (
                      <Button key={rIndex} style={{ width: '100%', marginBottom: 4 }}>
                        {row.listRowTitle}
                      </Button>
                    ))}
                  </div>
                ));
              }

              if (messageType === 'QUICKREPLY') {
                return JSON.parse(content).buttons.map((btn, bIndex) => (
                  <Button key={bIndex} style={{ width: '100%', marginBottom: 4 }}>
                    {btn.text}
                  </Button>
                ));
              }

              if (messageType === 'AUDIO') return <p>Ses dosyası gönderildi.</p>;
              if (messageType === 'IMAGE') return <p>Resim dosyası gönderildi.</p>;
              if (messageType === 'VIDEO') return <p>Video dosyası gönderildi.</p>;

              if (
                (messageType === 'COMMAND' && commandType === 'CLOSE') ||
                (messageType === 'ENDOFCONVERSATION' && content === 'CLOSE')
              ) {
                return <>Sohbet sonlandırıldı.</>;
              }

              if (messageType === 'COMMAND' && commandType === 'INTERNAL_HANDOVER') {
                return <>Sohbete temsilci atanıyor.</>;
              }

              return <p>Mesaj tipi desteklenmiyor.</p>;
            };

            const renderCard = (sentence, index) => (
              <Grid.Col span={12} key={index}>
                <Card
                  withBorder
                  shadow="md"
                  padding="md"
                  className="cursor-pointer"
                  style={{
                    width: '80%',
                    float: sentence.sendBy === 'CLIENT' ? 'left' : 'right',
                    clear: sentence.sendBy === 'CLIENT' ? 'left' : 'right',
                  }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <b
                      className="me-3 text-small"
                      style={{
                        marginLeft: sentence.sendBy === 'CLIENT' ? '0px' : 'auto',
                      }}
                    >
                      {sentence.sendBy +
                        ' (' +
                        new Date(sentence.date).toLocaleString('tr-TR', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                        }) +
                        ')'}
                    </b>
                    <p className="text-small" style={{ margin: '0px' }}>
                      {renderContent(sentence)}
                    </p>
                  </div>
                </Card>
              </Grid.Col>
            );

            return (
              <Accordion defaultValue="after">
                <Accordion.Item value="before">
                  <Accordion.Control>Temsilci Sohbete Katılmadan Önce</Accordion.Control>
                  <Accordion.Panel>
                    <Grid gutter="md" style={{ maxHeight: '500px', overflowY: 'scroll' }}>
                      {messagesBeforeAgent.map((item, index) => {
                        return renderCard(item, index);
                      })}
                    </Grid>
                  </Accordion.Panel>
                </Accordion.Item>
                <Accordion.Item value="after">
                  <Accordion.Control>Temsilci Sohbete Katıldıktan Sonra</Accordion.Control>
                  <Accordion.Panel>
                    <Grid gutter="md" style={{ maxHeight: '500px', overflowY: 'scroll' }}>
                      {messagesAfterAgent.map((item, index) => {
                        return renderCard(item, index);
                      })}
                    </Grid>
                  </Accordion.Panel>
                </Accordion.Item>
              </Accordion>
            );
          })()}
        </>
      )}
    </>
  );
}
