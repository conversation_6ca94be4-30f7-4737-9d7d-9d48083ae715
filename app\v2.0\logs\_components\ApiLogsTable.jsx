'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Group,
  Button,
  Text,
  Table,
  ScrollArea,
  Loader,
  Alert,
  Box,
  Badge,
  Pagination,
  Tooltip,
} from '@mantine/core';
import { IconFileExport, IconAlertCircle, IconRefresh } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import ExcelJS from 'exceljs';

// eslint-disable-next-line react/prop-types
const ApiLogsTable = ({ filters, fetchAuthClient }) => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    totalItems: 0,
  });

  const fetchLogs = async (page = 1) => {
    setLoading(true);
    setError(null);

    try {
      const requestBody = {
        page: page,
        pageSize: pagination.pageSize,
        columnFilters: filters,
      };

      const response = await fetchAuthClient('ApiLog/paged', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data = await response.json();
        setLogs(data.items || []);
        setPagination((prev) => ({
          ...prev,
          currentPage: data.currentPage || page,
          totalPages: data.totalPages || 0,
          totalItems: data.totalItems || 0,
          pageSize: data.pageSize || prev.pageSize,
        }));
      } else {
        setError('API logları yüklenirken bir hata oluştu.');
      }
    } catch (err) {
      console.error('Error fetching API logs:', err);
      setError('API logları yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs(1);
  }, [filters]);

  const exportToExcel = async () => {
    try {
      if (logs.length === 0) {
        notifications.show({
          title: 'Uyarı',
          message: 'Dışa aktarılacak veri bulunamadı.',
          color: 'orange',
        });
        return;
      }

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('API Logları');

      const headers = [
        'ID',
        'İstek Tarihi',
        'HTTP Method',
        'Path',
        'Query String',
        'Request Body',
        'Headers',
        'Kullanıcı ID',
        'Tenant Adı',
      ];

      worksheet.addRow(headers);

      logs.forEach((log) => {
        const row = [
          log.id || '',
          log.requestTime ? new Date(log.requestTime).toLocaleString('tr-TR') : '',
          log.method || '',
          log.path || '',
          log.queryString || '',
          log.body || '',
          log.headers || '',
          log.userId || '',
          log.tenantName || '',
        ];

        worksheet.addRow(row);
      });

      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6E6FA' },
      };

      worksheet.columns.forEach((column) => {
        let maxLength = 0;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const dateStr = new Date().toLocaleDateString('tr-TR').replace(/\//g, '_');
      link.download = `API_Loglari_${dateStr}.xlsx`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notifications.show({
        title: 'Başarılı',
        message: 'Excel dosyası başarıyla indirildi.',
        color: 'green',
      });
    } catch (err) {
      console.error('Excel export error:', err);
      notifications.show({
        title: 'Hata',
        message: 'Excel dosyası oluşturulurken bir hata oluştu.',
        color: 'red',
      });
    }
  };

  const getMethodBadgeColor = (method) => {
    switch (method?.toUpperCase()) {
      case 'GET':
        return 'blue';
      case 'POST':
        return 'green';
      case 'PUT':
        return 'orange';
      case 'DELETE':
        return 'red';
      case 'PATCH':
        return 'violet';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const dateStr = date.toLocaleDateString('tr-TR');
    const timeStr = date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
    return (
      <div style={{ lineHeight: 1.2 }}>
        <div>{dateStr}</div>
        <div style={{ fontSize: '0.85em', color: '#666' }}>{timeStr}</div>
      </div>
    );
  };

  const truncateText = (text, maxLength = 50) => {
    if (!text) return '-';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <Card withBorder>
        <Box style={{ textAlign: 'center', padding: '20px' }}>
          <Loader size="md" />
          <Text mt="md">API logları yükleniyor...</Text>
        </Box>
      </Card>
    );
  }

  return (
    <Card withBorder>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={500}>
          API Logları
        </Text>
        <Group>
          <Button leftSection={<IconFileExport size={16} />} onClick={exportToExcel} variant="filled" size="sm">
            Dışa Aktar
          </Button>
          <Button
            leftSection={<IconRefresh size={16} />}
            onClick={() => fetchLogs(pagination.currentPage)}
            variant="light"
            size="sm"
          >
            Yenile
          </Button>
        </Group>
      </Group>

      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Hata" color="red" mb="md">
          {error}
        </Alert>
      )}

      {logs.length === 0 && !loading ? (
        <Alert icon={<IconAlertCircle size={16} />} title="Veri Bulunamadı" color="gray">
          Seçilen filtrelere uygun API logu bulunamadı.
        </Alert>
      ) : (
        <>
          <Text size="sm" c="dimmed" mb="md">
            Toplam {pagination.totalItems} kayıt bulundu (Sayfa {pagination.currentPage} / {pagination.totalPages})
          </Text>

          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th w={80}>ID</Table.Th>
                  <Table.Th w={120}>İstek Tarihi</Table.Th>
                  <Table.Th w={100}>Method</Table.Th>
                  <Table.Th w={250}>Path</Table.Th>
                  <Table.Th w={150}>Query String</Table.Th>
                  <Table.Th w={150}>Request Body</Table.Th>
                  <Table.Th w={100}>Kullanıcı ID</Table.Th>
                  <Table.Th w={120}>Tenant</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {logs.map((log, index) => (
                  <Table.Tr key={log.id || index}>
                    <Table.Td>
                      <Text size="sm" fw={500}>
                        {log.id}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{formatDate(log.requestTime)}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getMethodBadgeColor(log.method)} size="sm">
                        {log.method || '-'}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Tooltip label={log.path} position="top-start" disabled={!log.path || log.path.length <= 30}>
                        <Text size="sm" style={{ wordBreak: 'break-all' }}>
                          {truncateText(log.path, 30)}
                        </Text>
                      </Tooltip>
                    </Table.Td>
                    <Table.Td>
                      <Tooltip
                        label={log.queryString}
                        position="top-start"
                        disabled={!log.queryString || log.queryString.length <= 20}
                      >
                        <Text size="sm" c={log.queryString ? 'inherit' : 'dimmed'}>
                          {truncateText(log.queryString, 20)}
                        </Text>
                      </Tooltip>
                    </Table.Td>
                    <Table.Td>
                      <Tooltip label={log.body} position="top-start" disabled={!log.body || log.body.length <= 20}>
                        <Text size="sm" c={log.body ? 'inherit' : 'dimmed'}>
                          {truncateText(log.body, 20)}
                        </Text>
                      </Tooltip>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c={log.userId ? 'inherit' : 'dimmed'}>
                        {log.userId || '-'}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c={log.tenantName ? 'inherit' : 'dimmed'}>
                        {log.tenantName || '-'}
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {pagination.totalPages > 1 && (
            <Group justify="center" mt="md">
              <Pagination
                total={pagination.totalPages}
                value={pagination.currentPage}
                onChange={(page) => fetchLogs(page)}
                size="sm"
              />
            </Group>
          )}
        </>
      )}
    </Card>
  );
};

export default ApiLogsTable;
