'use client';
import React from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { Card, Grid, Button, Group, Text } from '@mantine/core';
import { IconFileTypePdf, IconBuilding } from '@tabler/icons-react';
import { useContext, useEffect, useState } from 'react';
import DashboardSkeleton from './_components/DashboardSkeleton';
import WordCloudComp from './_components/WordCloudComp';
import AlarmStatus from './_components/AlarmStatus';
import CustomerSatisfaction from './_components/CustomerSatisfaction';
import TopCallReasons from './_components/TopCallReasons';
import WhereWeMakingMistake from './_components/WhereWeMakingMistake';
import TopFiveComplaints from './_components/TopFiveComplaints';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import { getDatesFromType } from '@/common/functions/commonFunctions';
import { useRouter } from 'next/navigation';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export default function Home() {
  const { permissions, fetchAuthClient, user } = useContext(AuthContext);
  const { getTenantParameters } = useContext(PluktoContext);
  const date = getDatesFromType('lastonemonth');
  const [dateFilterValue, setDateFilterValue] = useState(date);
  const [vendorFilterValue, setVendorFilterValue] = useState(null);
  const [callDashboardData, setCallDashboardData] = useState(null);
  const [chatDashboardData, setChatDashboardData] = useState(null);
  const [isExporting, setIsExporting] = useState(false);
  const [filters, setFilters] = useState([
    {
      id: 'date',
      value: date,
    },
    {
      id: 'isAnalysisCompleted',
      value: 'true',
    },
  ]);
  const router = useRouter();

  const fetchDashboardDatas = async () => {
    setChatDashboardData(null);
    setCallDashboardData(null);
    const [chatResponse, callResponse] = await Promise.all([
      fetchAuthClient(`Chat/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
      fetchAuthClient(`Call/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
    ]);
    const [chatData, callData] = await Promise.all([chatResponse.json(), callResponse.json()]);
    setChatDashboardData(chatData);
    setCallDashboardData(callData);
  };

  const navigateToChannelsWithFilters = (channelType, additionalParams = []) => {
    const queryParams = new URLSearchParams();
    queryParams.append('channelType', channelType);
    const tempFilters = [...filters, ...additionalParams];
    const tempFiltersJsonString = JSON.stringify(tempFilters);
    const utf8Bytes = new TextEncoder().encode(tempFiltersJsonString);
    const base64String = btoa(String.fromCharCode(...utf8Bytes));
    queryParams.append('filters', base64String);
    router.push(`/${process.env.VERSION}/channels?${queryParams.toString()}`); // eslint-disable-line no-undef
  };

  useEffect(() => {
    fetchDashboardDatas();
  }, [filters]);

  const isAgentUser = user?.role === 'ÇAĞRI MT' || user?.role === 'YAZIŞMA MT';

  if (isAgentUser) {
    if (user?.role === 'ÇAĞRI MT' && permissions.includes('Call.AgentView')) {
      window.location.href = '/' + process.env.VERSION + '/channels/agents?channelType=Call';
      return;
    } else if (user?.role === 'YAZIŞMA MT' && permissions.includes('Chat.AgentView')) {
      window.location.href = '/' + process.env.VERSION + '/channels/agents?channelType=Chat';
      return;
    } else {
      if (permissions.includes('Call.View')) {
        window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call';
        return;
      } else if (permissions.includes('Chat.View')) {
        window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat';
        return;
      } else {
        window.location.href = '/401';
        return;
      }
    }
  }

  if (permissions.includes('Call.View') && permissions.includes('Chat.View')) {
    // Both permissions
  } else if (permissions.includes('Call.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call'; // eslint-disable-line no-undef
    return;
  } else if (permissions.includes('Chat.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat'; // eslint-disable-line no-undef
    return;
  } else {
    window.location.href = '/401';
    return;
  }

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);

    try {
      const f = (d) =>
        `${d.getDate().toString().padStart(2, '0')}_${(d.getMonth() + 1)
          .toString()
          .padStart(2, '0')}_${d.getFullYear()}_${d.getHours().toString().padStart(2, '0')}_${d
          .getMinutes()
          .toString()
          .padStart(2, '0')}_${d.getSeconds().toString().padStart(2, '0')}`;

      const element = document.getElementById('pdf-content');
      if (!element) return;

      // PDF için geçici stil uygula
      const originalStyle = element.style.cssText;
      const originalWidth = element.style.width;

      // PDF için sabit genişlik ayarla (A4 boyutuna uygun)
      element.style.width = '1200px';
      element.style.maxWidth = '1200px';
      element.style.minWidth = '1200px';
      element.style.transform = 'scale(1)';
      element.style.transformOrigin = 'top left';

      // Kısa bir süre bekle ki stil değişiklikleri uygulanabilsin
      await new Promise(resolve => setTimeout(resolve, 200));

      const canvas = await html2canvas(element, {
        scrollY: -window.scrollY,
        useCORS: true,
        allowTaint: true,
        scale: 2, // Daha yüksek çözünürlük için
        width: 1200, // Sabit genişlik
        windowWidth: 1200, // Pencere genişliği simülasyonu
        windowHeight: window.innerHeight,
        backgroundColor: '#ffffff',
        removeContainer: false,
        foreignObjectRendering: true,
        logging: false
      });

      const imgData = canvas.toDataURL('image/png', 1.0);
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Margin değerleri
      const margin = 10;
      const availableWidth = pdfWidth - (margin * 2);
      const availableHeight = pdfHeight - (margin * 2);

      const imgProps = pdf.getImageProperties(imgData);
      const imgWidth = imgProps.width;
      const imgHeight = imgProps.height;

      // Görüntüyü sayfa boyutuna sığdır
      const ratio = Math.min(availableWidth / (imgWidth * 0.264583), availableHeight / (imgHeight * 0.264583));
      const scaledWidth = (imgWidth * 0.264583) * ratio;
      const scaledHeight = (imgHeight * 0.264583) * ratio;

      // Ortalama konumlandırma
      const x = margin + (availableWidth - scaledWidth) / 2;
      const y = margin + (availableHeight - scaledHeight) / 2;

      // Eğer içerik çok uzunsa, çoklu sayfa oluştur
      if (scaledHeight > availableHeight) {
        const pageHeight = availableHeight;
        const totalPages = Math.ceil(scaledHeight / pageHeight);

        for (let i = 0; i < totalPages; i++) {
          if (i > 0) {
            pdf.addPage();
          }

          const sourceY = (imgHeight / totalPages) * i;
          const sourceHeight = imgHeight / totalPages;

          // Canvas'ın bir bölümünü al
          const pageCanvas = document.createElement('canvas');
          const pageCtx = pageCanvas.getContext('2d');
          pageCanvas.width = imgWidth;
          pageCanvas.height = sourceHeight;

          pageCtx.drawImage(canvas, 0, sourceY, imgWidth, sourceHeight, 0, 0, imgWidth, sourceHeight);
          const pageImgData = pageCanvas.toDataURL('image/png', 1.0);

          pdf.addImage(pageImgData, 'PNG', x, margin, scaledWidth, pageHeight);
        }
      } else {
        pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);
      }

      pdf.save(
        `${dateFilterValue[0] !== null ? f(dateFilterValue[0]) + '_' : ''}${
          dateFilterValue[0] !== null && dateFilterValue[1] !== null ? 've_' : ''
        }${dateFilterValue[1] !== null ? f(dateFilterValue[1]) + '_' : ''}deneyim_dashboardu_raporu.pdf`
      );

      // Orijinal stilleri geri yükle
      element.style.cssText = originalStyle;
      if (originalWidth) {
        element.style.width = originalWidth;
      }
    } catch (error) {
      console.error('PDF oluşturma hatası:', error);
      alert('PDF oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsExporting(false);
    }
  };

  const setFilter = (value, isEmpty, setFilterMethod, filterId) => {
    setFilterMethod(value);
    setFilters((prevFilters) => {
      const filtered = prevFilters.filter((filter) => filter.id !== filterId);
      if (isEmpty) {
        return [...filtered];
      } else {
        return [
          ...filtered,
          {
            id: filterId,
            value: value,
          },
        ];
      }
    });
  };

  return (
    <>
      {/* Filtreler ve PDF butonu - PDF'e dahil edilmeyecek */}
      <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 2 }}>
        <Group justify="space-between" align="center">
          <Group>
            <DateFilterPopover
              value={dateFilterValue}
              onChange={(value, isEmpty) => {
                setDateFilterValue(value);
                setFilters((prevFilters) => {
                  const filtered = prevFilters.filter((filter) => filter.id !== 'date');
                  if (isEmpty) {
                    return [...filtered];
                  } else {
                    return [
                      ...filtered,
                      {
                        id: 'date',
                        value: value,
                      },
                    ];
                  }
                });
              }}
              label={'Tarih Aralığı'}
              initDateTypeValue={'lastonemonth'}
            />
            <SelectFilterPopover
              value={vendorFilterValue}
              onChange={(value, isEmpty) => setFilter(value, isEmpty, setVendorFilterValue, 'vendor')}
              icon={<IconBuilding />}
              label={'Şirketler'}
              data={
                getTenantParameters('Call')?.vendors
                  ? getTenantParameters('Call').vendors.map((vendor) => ({
                      value: vendor,
                      label: vendor,
                    }))
                  : []
              }
            />
          </Group>
          <Button
            leftSection={<IconFileTypePdf size={18} />}
            variant="gradient"
            gradient={{ from: 'red', to: 'orange', deg: 45 }}
            onClick={handleExport}
            loading={isExporting}
            disabled={isExporting}
            size="sm"
            radius="md"
            style={{
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease',
            }}
            styles={{
              root: {
                '&:hover': {
                  transform: isExporting ? 'none' : 'translateY(-2px)',
                  boxShadow: isExporting ? '0 4px 12px rgba(0, 0, 0, 0.1)' : '0 6px 20px rgba(0, 0, 0, 0.15)',
                },
              },
            }}
          >
            <Text size="sm" fw={500}>
              {isExporting ? 'PDF Oluşturuluyor...' : 'PDF Olarak Kaydet'}
            </Text>
          </Button>
        </Group>
      </Card>

      {/* PDF içeriği - sadece bu kısım PDF'e dahil edilecek */}
      <div id="pdf-content" style={{ backgroundColor: 'white', padding: '20px' }}>
        {/* PDF için başlık */}
        <div style={{ marginBottom: '20px', textAlign: 'center', borderBottom: '2px solid #e9ecef', paddingBottom: '15px' }}>
          <Text size="xl" fw={700} style={{ color: '#333', marginBottom: '5px' }}>
            Deneyim Dashboard Raporu
          </Text>
          <Text size="sm" c="dimmed">
            {dateFilterValue[0] && dateFilterValue[1]
              ? `${dateFilterValue[0].toLocaleDateString('tr-TR')} - ${dateFilterValue[1].toLocaleDateString('tr-TR')}`
              : 'Tüm Zamanlar'
            }
            {vendorFilterValue && ` • ${vendorFilterValue}`}
          </Text>
        </div>

        {!callDashboardData || !chatDashboardData ? (
          <DashboardSkeleton />
        ) : (
          <Grid>
            <Grid.Col span={{ base: 12, md: 7 }}>
              <WordCloudComp
                dashboardDataCall={callDashboardData}
                dashboardDataChat={chatDashboardData}
                onItemClick={(item) => {
                  navigateToChannelsWithFilters(item.type, [
                    {
                      id: 'keywords',
                      value: [item.keywords],
                    },
                  ]);
                }}
              />
              <div style={{ marginTop: '1rem' }}>
                <CustomerSatisfaction
                  dashboardDataCall={callDashboardData}
                  dashboardDataChat={chatDashboardData}
                  filters={filters}
                  onItemClick={(item) => {
                    navigateToChannelsWithFilters(item.type, [
                      {
                        id: 'sentiment',
                        value: [item.sentiment],
                      },
                    ]);
                  }}
                />
              </div>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 5 }}>
              <AlarmStatus
                dashboardDataCall={callDashboardData}
                dashboardDataChat={chatDashboardData}
                onItemClick={(item) => {
                  navigateToChannelsWithFilters(item.type, [
                    {
                      id: 'alarmCategories',
                      value: [item.alarmCategory],
                    },
                  ]);
                }}
              />
            </Grid.Col>
            <Grid.Col span={12}>
              <Grid>
                <Grid.Col span={{ base: 12, md: 6 }}>
                  <TopCallReasons
                    dashboardDataCall={callDashboardData}
                    dashboardDataChat={chatDashboardData}
                    onItemClick={(item) => {
                      navigateToChannelsWithFilters(item.type, [
                        {
                          id: 'category',
                          value: [item.category],
                        },
                      ]);
                    }}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 6 }}>
                  <WhereWeMakingMistake
                    dashboardDataCall={callDashboardData}
                    dashboardDataChat={chatDashboardData}
                    onItemClick={(item) => {
                      navigateToChannelsWithFilters(item.type, [
                        {
                          id: 'analysis',
                          value: [item.analysis],
                        },
                      ]);
                    }}
                  />
                </Grid.Col>
              </Grid>
            </Grid.Col>
            <Grid.Col span={12}>
              <TopFiveComplaints
                dashboardDataCall={callDashboardData}
                dashboardDataChat={chatDashboardData}
                onItemClick={(item) => {
                  navigateToChannelsWithFilters(item.type, [
                    {
                      id: 'subCategory',
                      value: [item.subCategory],
                    },
                  ]);
                }}
              />
            </Grid.Col>
          </Grid>
        )}
      </div>
    </>
  );
}
