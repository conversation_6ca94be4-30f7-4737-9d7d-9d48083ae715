"use client"

import React, { useContext, useEffect, useState } from "react";
import {
    Modal,
    ScrollArea,
    Button,
    Text,
    Menu,
    Card,
    Grid,
    Textarea,
    TextInput,
    Select,
    Accordion,
} from "@mantine/core";
import {
    IconLetterCase,
    IconNumber123,
    IconArrowUp,
    IconArrowDown,
} from "@tabler/icons-react";
import { PluktoContext } from "@/common/contexts/PluktoContext";
import { modals } from "@mantine/modals";
import { getDateRangeTextFromFilters, getDatesFromType } from "@/common/functions/commonFunctions";
import { AuthContext } from "@/common/contexts/AuthContext";
import { handleFetchSubmit } from "@/common/functions/formFunctions";

function moveItem(arr, from, to) {
    if (to < 0 || to >= arr.length) return arr;
    const newArr = [...arr];
    const [removed] = newArr.splice(from, 1);
    newArr.splice(to, 0, removed);
    return newArr;
}

function PivotModal({
    opened,
    onClose,
    editedReportProp,
    filters
}) {
    const { getTenantParameters } = useContext(PluktoContext);
    const [editedReport, setEditedReport] = useState(editedReportProp);
    const { fetchAuthClient } = useContext(AuthContext);

    useEffect(() => {
        setEditedReport(editedReportProp);
    }, [editedReportProp]);

    if (editedReportProp === undefined || editedReport === undefined) {
        return <></>
    }

    const columns = [
        {
            groupName: "Ortak Değişkenler",
            groups: [
                { fieldName: "id", name: "Id", label: "Id", type: "int" },
                { fieldName: "channel", name: "Kanal", label: "Kanal", type: "string" },
                { fieldName: "vendor", name: "Firma", label: "Firma", type: "string" },
                { fieldName: "agent", name: "Temsilci", label: "Temsilci", type: "string" },
                { fieldName: "agentId", name: "Temsilci Id", label: "Temsilci Id", type: "int" },
                { fieldName: "category", name: "Ana Kategori", label: "Ana Kategori", type: "string" },
                { fieldName: "subCategory", name: "Alt Kategori", label: "Alt Kategori", type: "string" },
                { fieldName: "point", name: "Kalite Puanı", label: "Kalite Puanı", type: "int" },
                { fieldName: "date", name: "Tarih ve Saat", label: "Tarih ve Saat", type: "datetime" },
                { fieldName: "onlyDate", name: "Tarih", label: "Tarih", type: "date" },
                { fieldName: "onlyYear", name: "Yıl", label: "Yıl", type: "int" },
                { fieldName: "onlyMonth", name: "Ay", label: "Ay", type: "string" },
                { fieldName: "onlyDay", name: "Gün", label: "Gün", type: "int" },
                { fieldName: "duration", name: "Süre", label: "Süre", type: "int" },
                { fieldName: "maxSlience", name: "Maksimum Sessizlik", label: "Maksimum Sessizlik", type: "int" },
                { fieldName: "language", name: "Dil", label: "Dil", type: "string" },
                { fieldName: "identifier", name: "Kurumdaki Id", label: "Kurumdaki Id", type: "string" },
            ]
        },
        {
            groupName: "Sadece Çağrı Değişkenleri",
            groups: [
                { fieldName: "type", name: "Ses Dosyası Tipi", label: "Ses Dosyası Tipi", type: "string" },
                { fieldName: "soundFile", name: "Ses Dosyası Adı(S3)", label: "Ses Dosyası Adı(S3)", type: "string" },
            ]
        },
        {
            groupName: "Sadece Yazışma Değişkenleri",
            groups: [
                { fieldName: "customerRating", name: "Müşteri Puanı", label: "Müşteri Puanı", type: "int" },
                { fieldName: "customerComment", name: "Müşteri Yorumu", label: "Müşteri Yorumu", type: "string" },
            ]
        },
        {
            groupName: "Çağrı Kalite Kuralı Değişkenleri",
            groups: [
                ...(getTenantParameters("Call")?.qualityRules || []).map(x => ({
                    fieldName: x + " Hatalı Mı",
                    name: x + " Hatalı Mı",
                    label: x + " Hatalı Mı"
                })),
                ...(getTenantParameters("Call")?.qualityRules || []).map(x => ({
                    fieldName: x + " Hatasız Mı",
                    name: x + " Hatasız Mı",
                    label: x + " Hatasız Mı"
                })),
            ]
        },
        {
            groupName: "Yazışma Kalite Kuralı Değişkenleri",
            groups: [
                ...(getTenantParameters("Chat")?.qualityRules || []).map(x => ({
                    fieldName: x + " Hatalı Mı",
                    name: x + " Hatalı Mı",
                    label: x + " Hatalı Mı"
                })),
                ...(getTenantParameters("Chat")?.qualityRules || []).map(x => ({
                    fieldName: x + " Hatasız Mı",
                    name: x + " Hatasız Mı",
                    label: x + " Hatasız Mı"
                })),
            ]
        }
    ];

    const isUsed = (fieldName) => (
        editedReport.setting.rowFields.some(x => x.fieldName === fieldName) ||
        editedReport.setting.columnFields.some(x => x.fieldName === fieldName) ||
        editedReport.setting.values.some(x => x.fieldName === fieldName)
    );

    return (
        <Modal opened={opened} title="Rapor Oluştur" onClose={onClose} size="90%" radius="lg">
            <Grid mb="md">
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Menu position="bottom" withArrow>
                        <Menu.Target>
                            <Button style={{ marginTop: "22px" }} fullWidth >İşlemler</Button>
                        </Menu.Target>
                        <Menu.Dropdown>
                            <Menu.Item onClick={async () => {
                                const response = await fetchAuthClient('Report/export/test', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        insertData: true,
                                        columnFilters: filters,
                                        reportSetting: editedReport.setting
                                    }),
                                });
                                const blob = await response.blob();
                                const url = URL.createObjectURL(blob);
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = editedReport.name.replace(" ", "_") + "_" + getDateRangeTextFromFilters(filters) + '.xlsx';
                                document.body.appendChild(link);
                                link.click();
                                link.remove();
                                URL.revokeObjectURL(url);
                            }}>
                                Test Exceli İndir
                            </Menu.Item>
                            <Menu.Item onClick={() => {
                                handleFetchSubmit(
                                    fetchAuthClient('Report/update', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({
                                            id: editedReport.id,
                                            name: editedReport.name,
                                            description: editedReport.description,
                                            setting: {
                                                channelType: editedReport.setting.channelType,
                                                rowFields: editedReport.setting.rowFields,
                                                columnFields: editedReport.setting.columnFields,
                                                values: editedReport.setting.values,
                                                unpivotRules: editedReport.setting.unpivotRules,
                                            },
                                        }),
                                    }),
                                    async (response) => {
                                        onClose();
                                    }
                                );
                            }}>
                                Güncelle
                            </Menu.Item>
                        </Menu.Dropdown>
                    </Menu>
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <TextInput
                        label="Rapor Adı"
                        value={editedReport.name}
                        onChange={(event) => setEditedReport(prev => ({ ...prev, name: event.currentTarget.value }))}
                    />
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Select
                        data={[
                            { value: "All", label: "Hepsi" },
                            { value: "Call", label: "Çağrı" },
                            { value: "Chat", label: "Yazışma" }
                        ]}
                        label="Rapor Kanalları"
                        value={editedReport.setting.channelType}
                        onChange={val => setEditedReport(prev => ({
                            ...prev,
                            setting: { ...prev.setting, channelType: val }
                        }))}
                    />
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Select
                        data={[
                            { value: "true", label: "Var" },
                            { value: "false", label: "Yok" }
                        ]}
                        label="Unpivot İşlemi"
                        value={(editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? "true" : "false"}
                        onChange={(val) => {
                            if (val === "true") {
                                setEditedReport(prev => ({
                                    ...prev,
                                    setting: {
                                        ...prev.setting,
                                        unpivotRules: {
                                            ruleKeyName: "Kural",
                                            ruleValueName: "Değer",
                                            columns: []
                                        }
                                    }
                                }));
                            } else {
                                setEditedReport(prev => ({
                                    ...prev,
                                    setting: { ...prev.setting, unpivotRules: undefined }
                                }));
                            }
                        }}
                    />
                </Grid.Col>
                {(editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) && (
                    <>
                        <Grid.Col span={{ md: 2 }}>
                            <TextInput
                                label="Unpivot Sütün Adı"
                                value={editedReport.setting.unpivotRules.ruleKeyName}
                                onChange={(event) =>
                                    setEditedReport(prev => ({
                                        ...prev,
                                        setting: {
                                            ...prev.setting,
                                            unpivotRules: {
                                                ...prev.setting.unpivotRules,
                                                ruleKeyName: event.currentTarget.value
                                            }
                                        }
                                    }))
                                }
                            />
                        </Grid.Col>
                        <Grid.Col span={{ md: 2 }}>
                            <TextInput
                                label="Unpivot Değer Adı"
                                value={editedReport.setting.unpivotRules.ruleValueName}
                                onChange={(event) =>
                                    setEditedReport(prev => ({
                                        ...prev,
                                        setting: {
                                            ...prev.setting,
                                            unpivotRules: {
                                                ...prev.setting.unpivotRules,
                                                ruleValueName: event.currentTarget.value
                                            }
                                        }
                                    }))
                                }
                            />
                        </Grid.Col>
                    </>
                )}
                <Grid.Col span={{ md: 12 }}>
                    <Textarea
                        label="Rapor Açıklaması"
                        value={editedReport.description}
                        onChange={(event) => setEditedReport(prev => ({ ...prev, description: event.currentTarget.value }))}
                        minRows={5}
                        resize="vertical"
                    />
                </Grid.Col>
            </Grid>
            <Grid>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Card withBorder shadow>
                        <Text size="xl">Değişkenler</Text>
                        <ScrollArea h={600} scrollbars="y">
                            <Accordion>
                                {columns.map((columnGroup, index) => (
                                    <Accordion.Item key={index} value={columnGroup.groupName}>
                                        <Accordion.Control>{columnGroup.groupName}</Accordion.Control>
                                        <Accordion.Panel>
                                            {columnGroup.groups.map((column, idx) => (
                                                <Menu position="right" withArrow key={idx}>
                                                    <Menu.Target>
                                                        <Button
                                                            disabled={isUsed(column.fieldName)}
                                                            style={{ display: "flex" }}
                                                            w="100%"
                                                            mt="md"
                                                        >
                                                            {column.type === "string" && (
                                                                <IconLetterCase style={{ marginRight: "8px" }} />
                                                            )}
                                                            {column.type === "int" && (
                                                                <IconNumber123 style={{ marginRight: "8px" }} />
                                                            )}
                                                            <span>{column.name || column.label}</span>
                                                        </Button>
                                                    </Menu.Target>
                                                    <Menu.Dropdown>
                                                        <Menu.Item onClick={() => {
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    rowFields: [
                                                                        ...prev.setting.rowFields,
                                                                        { fieldName: column.fieldName, label: column.name || column.label }
                                                                    ]
                                                                }
                                                            }))
                                                        }}>
                                                            Satırlara Ekle
                                                        </Menu.Item>
                                                        <Menu.Item onClick={() => {
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    columnFields: [
                                                                        ...prev.setting.columnFields,
                                                                        { fieldName: column.fieldName, label: column.name || column.label }
                                                                    ]
                                                                }
                                                            }))
                                                        }}>
                                                            Sütunlara Ekle
                                                        </Menu.Item>
                                                        <Menu.Item onClick={() => {
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    values: [
                                                                        ...prev.setting.values,
                                                                        {
                                                                            fieldName: column.fieldName,
                                                                            label: column.name || column.label,
                                                                            agg: "sum",
                                                                            calculation: "normal",
                                                                            format: ""
                                                                        }
                                                                    ]
                                                                }
                                                            }))
                                                        }}>
                                                            Değerlere Ekle
                                                        </Menu.Item>
                                                        {(editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) && (
                                                            <Menu.Item onClick={() => {
                                                                setEditedReport(prev => ({
                                                                    ...prev,
                                                                    setting: {
                                                                        ...prev.setting,
                                                                        unpivotRules: {
                                                                            ...prev.setting.unpivotRules,
                                                                            columns: [...prev.setting.unpivotRules.columns, {
                                                                                fieldName: column.fieldName,
                                                                                label: column.name || column.label,
                                                                            }]
                                                                        }
                                                                    }
                                                                }))
                                                            }}>
                                                                Unpivot Sütünlarına Ekle
                                                            </Menu.Item>
                                                        )}
                                                    </Menu.Dropdown>
                                                </Menu>
                                            ))}
                                        </Accordion.Panel>
                                    </Accordion.Item>
                                ))}
                            </Accordion>
                        </ScrollArea>
                    </Card>
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Card withBorder shadow>
                        <Text size="xl">Satırlar</Text>
                        <ScrollArea h={600} scrollbars="y">
                            {editedReport.setting.rowFields.map((row, index) => (
                                <Menu key={index} position="right" withArrow>
                                    <Menu.Target>
                                        <Button
                                            style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                            w="100%"
                                            mt="md"
                                        >
                                            <span style={{ display: "flex", gap: 4, marginLeft: "8px" }}>
                                                {index !== 0 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    rowFields: moveItem(prev.setting.rowFields, index, index - 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowUp size={16} />
                                                    </Button>
                                                )}
                                                {index !== editedReport.setting.rowFields.length - 1 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    rowFields: moveItem(prev.setting.rowFields, index, index + 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowDown size={16} />
                                                    </Button>
                                                )}
                                            </span>
                                            <span>{row.label}</span>
                                        </Button>
                                    </Menu.Target>
                                    <Menu.Dropdown>
                                        <Menu.Item onClick={() => {
                                            setEditedReport(prev => ({
                                                ...prev,
                                                setting: {
                                                    ...prev.setting,
                                                    rowFields: prev.setting.rowFields.filter(x => x.fieldName !== row.fieldName)
                                                }
                                            }))
                                        }}>
                                            Kaldır
                                        </Menu.Item>
                                        <Menu.Item onClick={() => {
                                            let tempRow = { ...row };
                                            modals.openConfirmModal({
                                                title: `Satırı Güncelle (${row.label})`,
                                                closeOnConfirm: true,
                                                labels: { confirm: "Güncelle", cancel: "İptal" },
                                                children: (
                                                    <TextInput
                                                        mb="md"
                                                        label="Tablodaki İsmi"
                                                        defaultValue={tempRow.label}
                                                        onChange={(event) => {
                                                            tempRow.label = event.currentTarget.value;
                                                        }}
                                                    />
                                                ),
                                                onConfirm: () => {
                                                    setEditedReport(prev => {
                                                        let filtered = prev.setting.rowFields.filter(x => x.fieldName !== row.fieldName);
                                                        return {
                                                            ...prev,
                                                            setting: {
                                                                ...prev.setting,
                                                                rowFields: [...filtered, tempRow]
                                                            }
                                                        };
                                                    });
                                                }
                                            });
                                        }}>
                                            Düzenle
                                        </Menu.Item>
                                    </Menu.Dropdown>
                                </Menu>
                            ))}
                        </ScrollArea>
                    </Card>
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Card withBorder shadow>
                        <Text size="xl">Sütunlar</Text>
                        <ScrollArea h={600} scrollbars="y">
                            {editedReport.setting.columnFields.map((col, index) => (
                                <Menu key={index} position="right" withArrow>
                                    <Menu.Target>
                                        <Button
                                            style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                            w="100%"
                                            mt="md"
                                        >
                                            <span style={{ display: "flex", gap: 4, marginLeft: "8px" }}>
                                                {index !== 0 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    columnFields: moveItem(prev.setting.columnFields, index, index - 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowUp size={16} />
                                                    </Button>
                                                )}
                                                {index !== editedReport.setting.columnFields.length - 1 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    columnFields: moveItem(prev.setting.columnFields, index, index + 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowDown size={16} />
                                                    </Button>
                                                )}
                                            </span>
                                            <span>{col.label}</span>
                                        </Button>
                                    </Menu.Target>
                                    <Menu.Dropdown>
                                        <Menu.Item onClick={() => {
                                            setEditedReport(prev => ({
                                                ...prev,
                                                setting: {
                                                    ...prev.setting,
                                                    columnFields: prev.setting.columnFields.filter(x => x.fieldName !== col.fieldName)
                                                }
                                            }))
                                        }}>
                                            Kaldır
                                        </Menu.Item>
                                        <Menu.Item onClick={() => {
                                            let tempCol = { ...col };
                                            modals.openConfirmModal({
                                                title: `Sütunu Güncelle (${col.label})`,
                                                closeOnConfirm: true,
                                                labels: { confirm: "Güncelle", cancel: "İptal" },
                                                children: (
                                                    <TextInput
                                                        mb="md"
                                                        label="Tablodaki İsmi"
                                                        defaultValue={tempCol.label}
                                                        onChange={(event) => {
                                                            tempCol.label = event.currentTarget.value;
                                                        }}
                                                    />
                                                ),
                                                onConfirm: () => {
                                                    setEditedReport(prev => {
                                                        let filtered = prev.setting.columnFields.filter(x => x.fieldName !== col.fieldName);
                                                        return {
                                                            ...prev,
                                                            setting: {
                                                                ...prev.setting,
                                                                columnFields: [...filtered, tempCol]
                                                            }
                                                        };
                                                    });
                                                }
                                            });
                                        }}>
                                            Düzenle
                                        </Menu.Item>
                                    </Menu.Dropdown>
                                </Menu>
                            ))}
                        </ScrollArea>
                    </Card>
                </Grid.Col>
                <Grid.Col span={{ md: (editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) ? 2 : 3 }}>
                    <Card withBorder shadow>
                        <Text size="xl">Değerler</Text>
                        <ScrollArea h={600} scrollbars="y">
                            {editedReport.setting.values.map((value, index) => (
                                <Menu key={index} position="right" withArrow>
                                    <Menu.Target>
                                        <Button
                                            style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                            w="100%"
                                            mt="md"
                                        >
                                            <span style={{ display: "flex", gap: 4, marginLeft: "8px" }}>
                                                {index !== 0 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    values: moveItem(prev.setting.values, index, index - 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowUp size={16} />
                                                    </Button>
                                                )}
                                                {index !== editedReport.setting.values.length - 1 && (
                                                    <Button
                                                        color="yellow"
                                                        px={6}
                                                        size="xs"
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            setEditedReport(prev => ({
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    values: moveItem(prev.setting.values, index, index + 1)
                                                                }
                                                            }))
                                                        }}
                                                    >
                                                        <IconArrowDown size={16} />
                                                    </Button>
                                                )}
                                            </span>
                                            <span>{value.label}</span>
                                        </Button>
                                    </Menu.Target>
                                    <Menu.Dropdown>
                                        <Menu.Item onClick={() => {
                                            setEditedReport(prev => ({
                                                ...prev,
                                                setting: {
                                                    ...prev.setting,
                                                    values: prev.setting.values.filter(x => x.fieldName !== value.fieldName)
                                                }
                                            }))
                                        }}>
                                            Kaldır
                                        </Menu.Item>
                                        <Menu.Item onClick={() => {
                                            let tempValue = { ...value };
                                            if (tempValue.calculation === undefined) {
                                                tempValue.calculation = "normal";
                                            }
                                            if (tempValue.agg === undefined) {
                                                tempValue.agg = "sum";
                                            }
                                            modals.openConfirmModal({
                                                title: `Değeri Güncelle (${value.label})`,
                                                closeOnConfirm: true,
                                                labels: { confirm: "Güncelle", cancel: "İptal" },
                                                children: (
                                                    <>
                                                        <TextInput
                                                            mb="md"
                                                            label="Tablodaki İsmi"
                                                            defaultValue={tempValue.label}
                                                            onChange={(event) => {
                                                                tempValue.label = event.currentTarget.value;
                                                            }}
                                                        />
                                                        <Select
                                                            data={[
                                                                { value: "sum", label: "Toplam" },
                                                                { value: "average", label: "Ortalama" },
                                                                { value: "count", label: "Say" },
                                                                { value: "min", label: "En Düşük" },
                                                                { value: "max", label: "En Yüksek" }
                                                            ]}
                                                            label="Formül"
                                                            value={tempValue.agg}
                                                            onChange={(val) => {
                                                                tempValue.agg = val;
                                                            }}
                                                        />
                                                        <Select
                                                            data={[
                                                                { value: "normal", label: "Normal" },
                                                                { value: "percentage", label: "Yüzdelik" }
                                                            ]}
                                                            label="Hesaplama"
                                                            value={tempValue.calculation}
                                                            onChange={(val) => {
                                                                tempValue.calculation = val;
                                                            }}
                                                        />
                                                        <TextInput
                                                            mb="md"
                                                            label="Gösterim Formatı"
                                                            defaultValue={tempValue.format}
                                                            onChange={(event) => {
                                                                tempValue.format = event.currentTarget.value;
                                                            }}
                                                        />
                                                    </>
                                                ),
                                                onConfirm: () => {
                                                    setEditedReport(prev => {
                                                        let filtered = prev.setting.values.filter(x => x.fieldName !== value.fieldName);
                                                        return {
                                                            ...prev,
                                                            setting: {
                                                                ...prev.setting,
                                                                values: [...filtered, tempValue]
                                                            }
                                                        };
                                                    });
                                                }
                                            });
                                        }}>
                                            Düzenle
                                        </Menu.Item>
                                    </Menu.Dropdown>
                                </Menu>
                            ))}
                        </ScrollArea>
                    </Card>
                </Grid.Col>
                {(editedReport.setting.unpivotRules !== undefined && editedReport.setting.unpivotRules !== null) && (
                    <Grid.Col span={{ md: 4 }}>
                        <Card withBorder shadow>
                            <Text size="xl">Unpivot Sütünları</Text>
                            <ScrollArea h={600} scrollbars="y">
                                {editedReport.setting.unpivotRules.columns.map((column, index) => (
                                    <Menu key={index} position="right" withArrow>
                                        <Menu.Target>
                                            <Button
                                                style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
                                                w="100%"
                                                mt="md"
                                            >
                                                <span style={{ display: "flex", gap: 4, marginRight: "8px" }}>
                                                    {index !== 0 && (
                                                        <Button
                                                            color="yellow"
                                                            px={6}
                                                            size="xs"
                                                            onClick={e => {
                                                                e.stopPropagation();
                                                                setEditedReport(prev => ({
                                                                    ...prev,
                                                                    setting: {
                                                                        ...prev.setting,
                                                                        unpivotRules: {
                                                                            ...prev.setting.unpivotRules,
                                                                            columns: moveItem(prev.setting.unpivotRules.columns, index, index - 1)
                                                                        }
                                                                    }
                                                                }))
                                                            }}
                                                        >
                                                            <IconArrowUp size={16} />
                                                        </Button>
                                                    )}
                                                    {index !== editedReport.setting.unpivotRules.columns.length - 1 && (
                                                        <Button
                                                            color="yellow"
                                                            px={6}
                                                            size="xs"
                                                            onClick={e => {
                                                                e.stopPropagation();
                                                                setEditedReport(prev => ({
                                                                    ...prev,
                                                                    setting: {
                                                                        ...prev.setting,
                                                                        unpivotRules: {
                                                                            ...prev.setting.unpivotRules,
                                                                            columns: moveItem(prev.setting.unpivotRules.columns, index, index + 1)
                                                                        }
                                                                    }
                                                                }))
                                                            }}
                                                        >
                                                            <IconArrowDown size={16} />
                                                        </Button>
                                                    )}
                                                </span>
                                                <span>{column.label}</span>
                                            </Button>
                                        </Menu.Target>
                                        <Menu.Dropdown>
                                            <Menu.Item onClick={() => {
                                                setEditedReport(prev => ({
                                                    ...prev,
                                                    setting: {
                                                        ...prev.setting,
                                                        unpivotRules: {
                                                            ...prev.setting.unpivotRules,
                                                            columns: prev.setting.unpivotRules.columns.filter(x => x.fieldName !== column.fieldName)
                                                        }
                                                    }
                                                }))
                                            }}>
                                                Kaldır
                                            </Menu.Item>
                                            <Menu.Item onClick={() => {
                                                let tempColumn = { ...column };
                                                modals.openConfirmModal({
                                                    title: `Değeri Güncelle (${column.label})`,
                                                    closeOnConfirm: true,
                                                    labels: { confirm: "Güncelle", cancel: "İptal" },
                                                    children: (
                                                        <TextInput
                                                            mb="md"
                                                            label="Tablodaki İsmi"
                                                            defaultValue={tempColumn.label}
                                                            onChange={(event) => {
                                                                tempColumn.label = event.currentTarget.value;
                                                            }}
                                                        />
                                                    ),
                                                    onConfirm: () => {
                                                        setEditedReport(prev => {
                                                            let filtered = prev.setting.unpivotRules.columns.filter(x => x.fieldName !== column.fieldName);
                                                            return {
                                                                ...prev,
                                                                setting: {
                                                                    ...prev.setting,
                                                                    unpivotRules: {
                                                                        ...prev.setting.unpivotRules,
                                                                        columns: [...filtered, tempColumn]
                                                                    }
                                                                }
                                                            };
                                                        });
                                                    }
                                                });
                                            }}>
                                                Düzenle
                                            </Menu.Item>
                                        </Menu.Dropdown>
                                    </Menu>
                                ))}
                            </ScrollArea>
                        </Card>
                    </Grid.Col>
                )}
            </Grid>
        </Modal>
    );
}

export default PivotModal;
