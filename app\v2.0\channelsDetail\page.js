'use client';
import React, { useContext, useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { Card, Container, Text, Title, Button, Group } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { ChannelDetailModal } from '../channels/(channelDetail)/ChannelDetailModal';

export default function ChannelDetailPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const { getTenantParameters, getAgents } = useContext(PluktoContext);
  const searchParams = useSearchParams();
  const router = useRouter();
  const channelType = searchParams.get('channelType');
  const id = searchParams.get('id');
  const [error, setError] = useState(null);

  const tenantParameters = getTenantParameters(channelType);
  const agents = getAgents(channelType);

  useEffect(() => {
    if (!channelType || !id) {
      setError('Geçersiz URL parametreleri. channelType ve id parametreleri gereklidir.');
      return;
    }

    if (!['Call', 'Chat'].includes(channelType)) {
      setError('Geçersiz kanal tipi. Call veya Chat olmalıdır.');
      return;
    }

    if (!permissions.includes(channelType + '.View')) {
      setError('Bu sayfayı görüntülemek için yetkiniz yok.');
      return;
    }
  }, [channelType, id, permissions]);

  const goBack = () => {
    if (channelType && permissions.includes(channelType + '.View')) {
      router.push(`/v2.0/channels?channelType=${channelType}`);
    } else {
      router.push('/');
    }
  };

  const getLabel = () => {
    if (channelType === 'Call') return 'Çağrı';
    if (channelType === 'Chat') return 'Yazışma';
    return channelType;
  };

  if (error) {
    return (
      <Container size="xl" py="md">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group mb="md">
            <Button variant="light" leftSection={<IconArrowLeft size={16} />} onClick={goBack}>
              Geri Dön
            </Button>
          </Group>
          <Text color="red" size="lg">
            {error}
          </Text>
        </Card>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Group mb="md">
        <Button variant="light" leftSection={<IconArrowLeft size={16} />} onClick={goBack}>
          Geri Dön
        </Button>
        <Title order={2}>
          {getLabel()} Detayı - ID: {id}
        </Title>
      </Group>

      <ChannelDetailModal
        channelType={channelType}
        id={id}
        tenantParameters={tenantParameters}
        agents={agents}
        fetchAuthClient={fetchAuthClient}
        permissions={permissions}
      />
    </Container>
  );
}
