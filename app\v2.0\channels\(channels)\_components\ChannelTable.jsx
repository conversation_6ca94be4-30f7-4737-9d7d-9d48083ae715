'use client';
import React, { useState } from 'react';
import { Box } from '@mantine/core';
import { TenantTable } from '@/common/components/TenantTable';
import { modals } from '@mantine/modals';
import { ChannelDetailModal } from '../../(channelDetail)/ChannelDetailModal';
import { ChannelForm } from './ChannelForm';

const ChannelTable = ({
  initColumnFilters,
  columns,
  tenantTableRef,
  channelType,
  label,
  user,
  agents,
  permissions,
  tenantParameters,
  fetchAuthClient,
  selectedItemActionButtons,
}) => {
  const [formFiles, setFormFiles] = useState(null);
  return (
    <Box mt="md">
      <TenantTable
        initSorting={[{ id: "date", desc: true }]}
        onBeforeFormSubmit={(formData) => {
          formData.set('date', new Date(formData.get('date')).toISOString());
          if (formFiles) {
            formFiles.forEach((x) => {
              formData.append('soundFiles', x);
            });
          }
          return formData;
        }}
        initColumnFilters={initColumnFilters}
        columns={columns}
        ref={tenantTableRef}
        entityType={channelType}
        entityText={label}
        form={(mode, tableForm) => ChannelForm(mode, tableForm, user, agents, setFormFiles)}
        allowAdd={channelType === 'Call'}
        entityToAdd={async () => {
          if (permissions.includes(channelType + '.Add')) {
            return { agentId: '' + user.id };
          } else {
            return {};
          }
        }}
        onRowClick={(data) => {
          modals.open({
            title: label + ' Detayı',
            size: '100%',
            overlayProps: {
              backgroundOpacity: 0.55,
              blur: 3,
            },
            radius: 'md',
            withCloseButton: true,
            children: (
              <ChannelDetailModal
                channelType={channelType}
                id={data.id}
                tenantParameters={tenantParameters}
                agents={agents}
                fetchAuthClient={fetchAuthClient}
                permissions={permissions}
              />
            ),
          });
        }}
        entityToUpdate={async (entity) => {
          return {
            id: entity.id,
            date: new Date(entity.date),
            agentId: entity.agentId ? '' + entity.agentId : null,
          };
        }}
        checkDelete={(data) => {
          return true;
        }}
        checkUpdate={(data) => {
          return true;
        }}
        selectedItemActionButtons={selectedItemActionButtons}
        stopPropagationSelector=".copy-button"
        agents={agents}
      />
    </Box>
  );
};

export default ChannelTable;
