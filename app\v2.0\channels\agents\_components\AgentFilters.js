'use client';
import React from 'react';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import { Button, Card, Group } from '@mantine/core';
import {
  IconUser,
  IconX,
  IconBuilding
} from '@tabler/icons-react';
import GroupedSelectFilterPopover from '@/common/components/filters/GroupedSelectFilterPopover';

export default function AgentFilters({
  agents,
  dateFilterValue,
  agentIdFilterValue,
  vendorFilterValue,
  initDateType,
  setFilter,
  clearFilters,
  tenantParameters,
}) {
  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 10 }}>
      <Group>
        <Button
          color="red"
          variant={'light'}
          onClick={clearFilters}
          leftSection={<IconX size={16} />}
          style={{ marginRight: 10 }}
        >
          Filtreleri Temizle
        </Button>
        <DateFilterPopover
          value={dateFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, 'dateFilterValue', 'date')}
          label={'Tarih Aralığı'}
          initDateTypeValue={initDateType}
        />
        <GroupedSelectFilterPopover
          label="Temsilci"
          icon={<IconUser />}
          value={agentIdFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, 'agentIdFilterValue', 'agentId')}
          groupedData={[
            {
              group: 'Aktif Temsilciler',
              options: agents.filter(user => user.extraJson?.IsActive === true).map(user => ({
                value: '' + user.id,
                label: `${user.name} ${user.surname}`,
              })),
            },
            {
              group: 'Pasif Temsilciler',
              options: agents.filter(user => user.extraJson?.IsActive === false).map(user => ({
                value: '' + user.id,
                label: `${user.name} ${user.surname}`,
              })),
            },
          ]}
          multiple
        />
        <SelectFilterPopover
          value={vendorFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, 'vendorFilterValue', 'vendor')}
          icon={<IconBuilding />}
          label={'Şirket'}
          data={
            tenantParameters.vendors
              ? tenantParameters.vendors.map((vendor) => ({
                value: vendor,
                label: vendor,
              }))
              : []
          }
        />
      </Group>
    </Card>
  );
}
