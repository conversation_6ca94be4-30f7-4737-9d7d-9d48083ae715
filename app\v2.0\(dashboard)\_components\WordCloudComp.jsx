import { Card, Paper, Text, Button, Switch, ActionIcon, Popover, Group, ScrollArea } from '@mantine/core';
import React, { useState, useEffect, useContext } from 'react';
import WordCloud from 'react-d3-cloud';
import { IconSettings } from '@tabler/icons-react';
import { AuthContext } from '@/common/contexts/AuthContext';
import Keyword<PERSON>low<PERSON>hart from './KeywordFlowChart';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const WordCloudComp = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  const [hiddenWords, setHiddenWords] = useState([]);
  const [settingsOpened, setSettingsOpened] = useState(false);
  const [allWords, setAllWords] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedWordDetails, setSelectedWordDetails] = useState(null);
  const [modalOpened, setModalOpened] = useState(false);
  const [keywordHierarchyData, setKeywordHierarchyData] = useState(null);
  const { fetchAuthClient } = useContext(AuthContext);

  const callKeywords = dashboardDataCall?.keywords || {};
  const chatKeywords = dashboardDataChat?.keywords || {};
  const allKeywordTexts = Array.from(new Set([...Object.keys(callKeywords), ...Object.keys(chatKeywords)]));


  const keywordsCategorySubCategory = dashboardDataCall?.keywordsCategorySubCategory || [];
  const chatKeywordsCategorySubCategory = dashboardDataChat?.keywordsCategorySubCategory || [];


  useEffect(() => {
    const fetchHiddenKeywords = async () => {
      try {
        const response = await fetchAuthClient('Tenant/keywords');
        if (response.ok) {
          const hiddenKeywordsList = await response.json();
          setHiddenWords(hiddenKeywordsList);
        } else {
          console.error('Failed to fetch hidden keywords');
        }
      } catch (error) {
        console.error('Error fetching hidden keywords:', error);
      }
    };

    if (fetchAuthClient) {
      fetchHiddenKeywords();
    }
  }, [fetchAuthClient]);

  useEffect(() => {
    const processedWords = allKeywordTexts
      .map((text) => {
        const callCount = callKeywords[text] || 0;
        const chatCount = chatKeywords[text] || 0;
        const totalCount = callCount + chatCount;
        return {
          text: text.toUpperCase(),
          value: totalCount,
          originalText: text,
          callCount,
          chatCount,
          totalCount,
        };
      })
      .sort((a, b) => b.totalCount - a.totalCount)
      .slice(0, 50);

    setAllWords(processedWords);
  }, [dashboardDataCall, dashboardDataChat]);

  const wordCloudData = allWords.filter((word) => !hiddenWords.includes(word.originalText)).slice(0, 20);

  const maxValue = wordCloudData.length > 0 ? Math.max(...wordCloudData.map((word) => word.value)) : 0;

  const toggleWordVisibility = async (word) => {
    if (isUpdating) return;

    setIsUpdating(true);

    let updatedHiddenWords;
    if (hiddenWords.includes(word)) {
      updatedHiddenWords = hiddenWords.filter((w) => w !== word);
    } else {
      updatedHiddenWords = [...hiddenWords, word];
    }
    setHiddenWords(updatedHiddenWords);

    try {
      const response = await fetchAuthClient('Tenant/keyword-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedHiddenWords),
      });

      if (!response.ok) {
        console.error('Failed to update hidden keywords');
        setHiddenWords(hiddenWords);
      }
    } catch (error) {
      console.error('Error updating hidden keywords:', error);
      setHiddenWords(hiddenWords);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleShowAllWords = async () => {
    if (isUpdating) return;

    setIsUpdating(true);

    try {
      const response = await fetchAuthClient('Tenant/keyword-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([]),
      });

      if (response.ok) {
        setHiddenWords([]);
      } else {
        console.error('Failed to show all keywords');
      }
    } catch (error) {
      console.error('Error showing all keywords:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const navigateToChannels = (keyword, channelType) => {
    if (onItemClick) {
      onItemClick({
        type: channelType,
        keywords: keyword.toLowerCase(),
      });
    } else {
      window.location.href = `/${process.env.VERSION}/channels?channelType=${channelType}&keywords=${encodeURIComponent(
        keyword.toLowerCase()
      )}`;
    }
  };


  const getKeywordHierarchy = async (keyword) => {
    const searchKeyword = keyword.toUpperCase();

    const matchingCallCategory = keywordsCategorySubCategory.find(
      (category) => category.name.toUpperCase() === searchKeyword
    );

    let callHierarchy = null;
    if (matchingCallCategory) {
      callHierarchy = [matchingCallCategory];
    } else {
      for (const category of keywordsCategorySubCategory) {
        if (category.subTitles) {
          const matchingSubCategory = category.subTitles.find(
            (subCategory) => subCategory.name.toUpperCase() === searchKeyword
          );

          if (matchingSubCategory) {
            callHierarchy = [
              {
                name: matchingSubCategory.name,
                count: matchingSubCategory.count,
                subTitles: matchingSubCategory.subTitles || [],
              },
            ];
            break;
          }
        }
      }
    }

    const matchingChatCategory = chatKeywordsCategorySubCategory.find(
      (category) => category.name.toUpperCase() === searchKeyword
    );

    let chatHierarchy = null;
    if (matchingChatCategory) {
      chatHierarchy = [matchingChatCategory];
    } else {
      for (const category of chatKeywordsCategorySubCategory) {
        if (category.subTitles) {
          const matchingSubCategory = category.subTitles.find(
            (subCategory) => subCategory.name.toUpperCase() === searchKeyword
          );

          if (matchingSubCategory) {
            chatHierarchy = [
              {
                name: matchingSubCategory.name,
                count: matchingSubCategory.count,
                subTitles: matchingSubCategory.subTitles || [],
              },
            ];
            break;
          }
        }
      }
    }

    let combinedHierarchy = null;
    if (callHierarchy || chatHierarchy) {
      const mergedCategories = new Map();

      if (callHierarchy) {
        callHierarchy.forEach((category) => {
          if (!mergedCategories.has(category.name)) {
            mergedCategories.set(category.name, {
              name: category.name,
              count: category.count || 0,
              subTitles: new Map(), 
            });
          } else {
            const existing = mergedCategories.get(category.name);
            existing.count += category.count || 0;
          }

          if (category.subTitles) {
            const existingCategory = mergedCategories.get(category.name);
            category.subTitles.forEach((sub) => {
              if (existingCategory.subTitles.has(sub.name)) {
                const existingSub = existingCategory.subTitles.get(sub.name);
                existingSub.count += sub.count || 0;
                if (sub.subTitles) {
                  if (!existingSub.subTitles) existingSub.subTitles = [];
                  const subSubMap = new Map();
                  
                  existingSub.subTitles.forEach(subSub => {
                    subSubMap.set(subSub.name, { ...subSub });
                  });
                  
                  sub.subTitles.forEach(subSub => {
                    if (subSubMap.has(subSub.name)) {
                      subSubMap.get(subSub.name).count += subSub.count || 0;
                    } else {
                      subSubMap.set(subSub.name, { ...subSub });
                    }
                  });
                  
                  existingSub.subTitles = Array.from(subSubMap.values());
                }
              } else {
                existingCategory.subTitles.set(sub.name, { 
                  ...sub, 
                  subTitles: sub.subTitles ? [...sub.subTitles] : []
                });
              }
            });
          }
        });
      }

      if (chatHierarchy) {
        chatHierarchy.forEach((category) => {
          if (!mergedCategories.has(category.name)) {
            mergedCategories.set(category.name, {
              name: category.name,
              count: category.count || 0,
              subTitles: new Map(),
            });
          } else {
            const existing = mergedCategories.get(category.name);
            existing.count += category.count || 0;
          }

          if (category.subTitles) {
            const existingCategory = mergedCategories.get(category.name);
            category.subTitles.forEach((sub) => {
              if (existingCategory.subTitles.has(sub.name)) {
                const existingSub = existingCategory.subTitles.get(sub.name);
                existingSub.count += sub.count || 0;
                if (sub.subTitles) {
                  if (!existingSub.subTitles) existingSub.subTitles = [];
                  const subSubMap = new Map();
                  
                  existingSub.subTitles.forEach(subSub => {
                    subSubMap.set(subSub.name, { ...subSub });
                  });
                  
                  sub.subTitles.forEach(subSub => {
                    if (subSubMap.has(subSub.name)) {
                      subSubMap.get(subSub.name).count += subSub.count || 0;
                    } else {
                      subSubMap.set(subSub.name, { ...subSub });
                    }
                  });
                  
                  existingSub.subTitles = Array.from(subSubMap.values());
                }
              } else {
                existingCategory.subTitles.set(sub.name, { 
                  ...sub, 
                  subTitles: sub.subTitles ? [...sub.subTitles] : []
                });
              }
            });
          }
        });
      }

      combinedHierarchy = Array.from(mergedCategories.values()).map(category => ({
        ...category,
        subTitles: Array.from(category.subTitles.values())
      }));
    }

    return {
      call: callHierarchy,
      chat: chatHierarchy,
      combined: combinedHierarchy,
    };
  };


  return (
    <>
      <Card withBorder p="md" radius="md" style={{ cursor: 'pointer' }} className="h-[35%] 3xl:h-[39%]">
        <Group position="apart" mb="xs">
          <Text c="black" size="xl" tt="uppercase" fw={700}>
            ANAHTAR KELİME BULUTU
          </Text>
          <Popover position="bottom-end" shadow="md" opened={settingsOpened} onChange={setSettingsOpened}>
            <Popover.Target>
              <ActionIcon
                variant="light"
                color="gray"
                onClick={() => setSettingsOpened((o) => !o)}
                title="Kelime görünürlüğünü ayarla"
              >
                <IconSettings size="1.2rem" />
              </ActionIcon>
            </Popover.Target>
            <Popover.Dropdown>
              <Text fw={500} mb="xs">
                Görünür Kelimeler
              </Text>
              <ScrollArea h={200} scrollbarSize={6}>
                {allKeywordTexts
                  .filter((text) => !hiddenWords.includes(text))
                  .map((text) => {
                    const callCount = callKeywords[text] || 0;
                    const chatCount = chatKeywords[text] || 0;
                    const totalCount = callCount + chatCount;

                    return (
                      <Group key={text} position="apart" mb="xs">
                        <Text size="sm">
                          {text.toUpperCase()} ({totalCount})
                        </Text>
                        <Switch
                          checked={true}
                          onChange={() => toggleWordVisibility(text)}
                          size="sm"
                          disabled={isUpdating}
                        />
                      </Group>
                    );
                  })}
              </ScrollArea>

              {hiddenWords.length > 0 && (
                <>
                  <Text fw={500} mt="md" mb="xs" c="dimmed">
                    Gizli Kelimeler
                  </Text>
                  <ScrollArea h={100} scrollbarSize={6} style={{ borderTop: '1px solid #eee', paddingTop: 8 }}>
                    {hiddenWords.map((word) => {
                      return (
                        <Group key={word} position="apart" mb="xs">
                          <Text size="sm" c="dimmed">
                            {word.toUpperCase()}
                          </Text>
                          <Button
                            variant="subtle"
                            color="blue"
                            size="xs"
                            compact
                            onClick={() => toggleWordVisibility(word)}
                            disabled={isUpdating}
                          >
                            Göster
                          </Button>
                        </Group>
                      );
                    })}
                  </ScrollArea>
                </>
              )}

              <Group position="right" mt="sm">
                <Button
                  variant="outline"
                  size="xs"
                  onClick={handleShowAllWords}
                  disabled={isUpdating || hiddenWords.length === 0}
                >
                  Tümünü Göster
                </Button>
              </Group>
            </Popover.Dropdown>
          </Popover>
        </Group>

        {wordCloudData && wordCloudData.length > 0 ? (
          <div className="w-full" style={{ height: '250px' }}>
            <WordCloud
              width={700}
              height={200}
              maxHeight={200}
              data={wordCloudData}
              font="Roboto"
              fontStyle="normal"
              fontWeight="bold"
              fontSize={(word) => {
                const minFontSize = 14;
                const maxFontSize = 38;

                let normalizedValue = 0;

                if (maxValue === 0 || maxValue === word.value) {
                  normalizedValue = 1;
                } else {
                  normalizedValue = 0.3 + (0.3 * word.value) / maxValue;
                }

                return minFontSize + normalizedValue * (maxFontSize - minFontSize);
              }}
              rotate={() => 0}
              padding={7}
              random={() => 0.5}
              fill={(word) => {
                const relativeValue = maxValue ? word.value / maxValue : 0;

                if (relativeValue > 0.85) return '#000000';
                if (relativeValue > 0.7) return '#222222';
                if (relativeValue > 0.55) return '#444444';
                if (relativeValue > 0.4) return '#666666';
                if (relativeValue > 0.25) return '#888888';
                if (relativeValue > 0.1) return '#AAAAAA';
                return '#CCCCCC';
              }}
              onWordClick={async (event, d) => {
                setSelectedWordDetails({
                  text: d.originalText.toUpperCase(),
                  callCount: d.callCount,
                  chatCount: d.chatCount,
                  totalCount: d.totalCount,
                });

                try {
                  const hierarchyData = await getKeywordHierarchy(d.originalText);
                  setKeywordHierarchyData(hierarchyData);
                } catch (error) {
                  console.error('Error fetching keyword hierarchy:', error);
                  setKeywordHierarchyData(null);
                }

                setModalOpened(true);
              }}
            />
          </div>
        ) : (
          <div style={{ width: '100%', height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Text color="dimmed">Henüz anahtar kelime verisi yok</Text>
          </div>
        )}
      </Card>

      <Dialog open={modalOpened} onOpenChange={(open) => {
        if (!open) {
          setModalOpened(false);
          setSelectedWordDetails(null);
          setKeywordHierarchyData(null);
        }
      }}>
        <DialogContent className="max-w-5xl h-[600px]">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              {selectedWordDetails?.text} - Anahtar Kelime Detayı
            </DialogTitle>
          </DialogHeader>
          {selectedWordDetails && (
            <>
              {keywordHierarchyData && (keywordHierarchyData.call || keywordHierarchyData.chat) ? (
                <KeywordFlowChart
                  keywordData={keywordHierarchyData}
                  onNavigate={navigateToChannels}
                  wordDetails={{
                    text: selectedWordDetails.text,
                    callCount: selectedWordDetails.callCount,
                    chatCount: selectedWordDetails.chatCount,
                    totalCount: selectedWordDetails.totalCount,
                  }}
                />
              ) : (
                <div style={{ padding: '20px' }}>
                  <Group position="apart" mb={16} spacing="xs">
                    <Text fz="sm" c="dimmed" fw={500} style={{ letterSpacing: '0.01em' }}>
                      Görüşme
                    </Text>
                    <Button
                      variant="light"
                      color="blue"
                      size="sm"
                      onClick={() => navigateToChannels(selectedWordDetails.text, 'Call')}
                      style={{
                        fontWeight: 600,
                        letterSpacing: '0.02em',
                      }}
                    >
                      {selectedWordDetails.callCount.toLocaleString('tr-TR')} Görüşme
                    </Button>
                  </Group>

                  <Group position="apart" mb={16} spacing="xs">
                    <Text fz="sm" c="dimmed" fw={500} style={{ letterSpacing: '0.01em' }}>
                      Yazışma
                    </Text>
                    <Button
                      variant="light"
                      color="orange"
                      size="sm"
                      onClick={() => navigateToChannels(selectedWordDetails.text, 'Chat')}
                      style={{
                        fontWeight: 600,
                        letterSpacing: '0.02em',
                      }}
                    >
                      {selectedWordDetails.chatCount.toLocaleString('tr-TR')} Yazışma
                    </Button>
                  </Group>

                  <Group position="apart" mt={20} style={{ borderTop: '1px solid #f1f3f5', paddingTop: '16px' }}>
                    <Text fz="md" c="dimmed" fw={500} style={{ letterSpacing: '0.01em' }}>
                      Toplam
                    </Text>
                    <Text fz="lg" fw={700} style={{ color: '#495057', letterSpacing: '0.02em' }}>
                      {selectedWordDetails.totalCount.toLocaleString('tr-TR')}
                    </Text>
                  </Group>
                </div>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default WordCloudComp;

