'use client';

import React, { useContext, useState } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { Card, Tabs, Container, Group, Button } from '@mantine/core';
import { IconRefresh, IconMessages, IconApi, IconUser } from '@tabler/icons-react';
import { getDatesFromType } from '@/common/functions/commonFunctions';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import GroupedSelectFilterPopover from '@/common/components/filters/GroupedSelectFilterPopover';
import QualityCommentsTable from './_components/QualityCommentsTable';
import ApiLogsTable from './_components/ApiLogsTable';

export default function LogsPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const { getAgents } = useContext(PluktoContext);
  const date = getDatesFromType('lastonemonth');

  const hasLogView = permissions.includes('Log.View');
  const hasCommentsView =permissions.includes('Comments.View');

  if (!hasLogView && !hasCommentsView) {
    window.location.href = '/401';
    return;
  }

  const [dateFilterValue, setDateFilterValue] = useState(date);
  const [userIdFilterValue, setUserIdFilterValue] = useState([]);
  const [activeTab, setActiveTab] = useState(hasCommentsView ? 'comments' : 'logs');

  const [filters, setFilters] = useState([
    {
      id: 'date',
      value: date,
    },
  ]);

  const setFilter = (value, isEmpty, setFilterMethod, filterId) => {
    setFilterMethod(value);
    setFilters((prevFilters) => {
      const filtered = prevFilters.filter((filter) => filter.id !== filterId);
      if (isEmpty) {
        return [...filtered];
      } else {
        return [
          ...filtered,
          {
            id: filterId,
            value: value,
          },
        ];
      }
    });
  };

  const clearFilters = () => {
    const defaultDate = getDatesFromType('lastonemonth');
    setDateFilterValue(defaultDate);
    setUserIdFilterValue([]);
    setFilters([
      {
        id: 'date',
        value: defaultDate,
      },
    ]);
  };

  const allAgents = [...getAgents('Call'), ...getAgents('Chat')];
  const uniqueAgents = allAgents.filter((agent, index, self) => index === self.findIndex((a) => a.id === agent.id));

  return (
    <Container size="xl" py="md">
      <Card withBorder mb="xl" padding="lg">
        <Group gap="md">
          <Button color="red" variant="light" onClick={clearFilters} leftSection={<IconRefresh size={16} />}>
            Filtreleri Temizle
          </Button>

          <DateFilterPopover
            value={dateFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setDateFilterValue, 'date')}
            label="Tarih Aralığı"
            initDateTypeValue="lastonemonth"
          />

          <GroupedSelectFilterPopover
            label="Kullanıcı"
            icon={<IconUser />}
            value={userIdFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setUserIdFilterValue, 'userId')}
            groupedData={[
              {
                group: 'Aktif Kullanıcılar',
                options: uniqueAgents
                  .filter((agent) => agent.extraJson?.IsActive === true)
                  .map((agent) => ({
                    value: agent.id.toString(),
                    label: `${agent.name || agent.userName || `Kullanıcı ${agent.id}`} ${agent.surname || ''}`.trim(),
                  })),
              },
              {
                group: 'Pasif Kullanıcılar',
                options: uniqueAgents
                  .filter((agent) => agent.extraJson?.IsActive === false)
                  .map((agent) => ({
                    value: agent.id.toString(),
                    label: `${agent.name || agent.userName || `Kullanıcı ${agent.id}`} ${agent.surname || ''}`.trim(),
                  })),
              },
            ]}
            multiple
          />
        </Group>
      </Card>

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List mb="lg">
          {hasCommentsView && (
            <Tabs.Tab value="comments" leftSection={<IconMessages size={16} />}>
              Kalite Yorumları
            </Tabs.Tab>
          )}
          {hasLogView && (
            <Tabs.Tab value="logs" leftSection={<IconApi size={16} />}>
              API Logları
            </Tabs.Tab>
          )}
        </Tabs.List>

        {hasCommentsView && (
          <Tabs.Panel value="comments">
            <QualityCommentsTable filters={filters} fetchAuthClient={fetchAuthClient} permissions={permissions} />
          </Tabs.Panel>
        )}

        {hasLogView && (
          <Tabs.Panel value="logs">
            <ApiLogsTable filters={filters} fetchAuthClient={fetchAuthClient} permissions={permissions} />
          </Tabs.Panel>
        )}
      </Tabs>
    </Container>
  );
}
