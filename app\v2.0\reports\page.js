'use client';

import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import {
  Card,
  Group,
  Button,
  Text,
  Checkbox,
  Stack,
  Container,
  SimpleGrid,
  Paper,
  ThemeIcon,
  ActionIcon,
} from '@mantine/core';
import {
  IconDownload,
  IconEdit,
  IconTrash,
  IconX
} from '@tabler/icons-react';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import { getDatesFromType } from '@/common/functions/commonFunctions';
import classes from './Reports.module.css';
import { notifications } from '@mantine/notifications';
import { useDisclosure } from '@mantine/hooks';
import PivotModal from './_components/PivotModal';
import { handleFetchSubmit } from '@/common/functions/formFunctions';
import { modals } from '@mantine/modals';

export default function ReportsPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const [opened, { open, close }] = useDisclosure(false);
  const [dateFilterValue, setDateFilterValue] = useState(getDatesFromType('lastonemonth'));
  const [reports, setReports] = useState([]);
  const [selectedReports, setSelectedReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [editedReport, setEditedReport] = useState(undefined);
  const [filters, setFilters] = useState([
    {
      id: 'date',
      value: getDatesFromType('lastonemonth'),
    },
    {
      id: 'isAnalysisCompleted',
      value: 'true',
    },
  ]);

  if (!permissions.includes('Report.View')) {
    window.location.href = '/401';
  }

  const setFilter = (value, isEmpty, setFilterMethod, filterId) => {
    setFilterMethod(value);
    setFilters((prevFilters) => {
      const filtered = prevFilters.filter((filter) => filter.id !== filterId);
      if (isEmpty) {
        return [...filtered];
      } else {
        return [
          ...filtered,
          {
            id: filterId,
            value: value,
          },
        ];
      }
    });
  };

  const clearFilters = () => {
    setDateFilterValue(getDatesFromType('lastonemonth'));
    setVendorFilterValue(null);
    setFilters([
      {
        id: 'date',
        value: getDatesFromType('lastonemonth'),
      },
      {
        id: 'isAnalysisCompleted',
        value: 'true',
      },
    ]);
  };

  useEffect(() => {
    const fetchReports = async () => {
      const response = await fetchAuthClient(`Report`, {
        method: 'GET',
      });
      const data = await response.json();
      setReports(data)
    }
    fetchReports();
  }, [])

  return (
    <Container size="xl" py="md">
      <Card withBorder mb="xl" padding="lg">
        <Group gap="md">
          <Button
            color="red"
            variant={'light'}
            onClick={clearFilters}
            leftSection={<IconX size={16} />}
            style={{ marginRight: 10 }}
          >
            Filtreleri Temizle
          </Button>
          <DateFilterPopover
            value={dateFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setDateFilterValue, 'date')}
            label="Tarih Aralığı"
            initDateTypeValue="lastonemonth"
          />
          {permissions.includes("Report.Add") && (
            <Button size="md" variant="filled" ms="auto" onClick={() => {
              handleFetchSubmit(
                fetchAuthClient('Report/add', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    name: "Rapor Adı",
                    description: "Rapor Açıklaması",
                    setting: {
                      channelType: "All",
                      rowFields: [],
                      columnFields: [],
                      values: []
                    },
                  }),
                }),
                async (response) => {
                  const response2 = await fetchAuthClient(`Report`, {
                    method: 'GET',
                  });
                  const data = await response2.json();
                  setReports(data)
                }
              );
            }}>Yeni Rapor Oluştur</Button>
          )}
          <Button
            size="md"
            disabled={selectedReports.length === 0}
            leftSection={<IconDownload size={18} />}
            loading={loading || isExporting}
            onClick={async () => {
              try {
                setLoading(true);
                setIsExporting(true);
                notifications.show({
                  id: 'export-process',
                  title: 'Raporlar Oluşturuluyor',
                  message: `${selectedReports.length} rapor oluşturulmaya başlandı...`,
                  color: 'blue',
                  loading: true,
                  autoClose: false,
                });
                const getDateRangeTextFromFilters = (filters) => {
                  const dateFilter = filters.find((filter) => filter.id === 'date');
                  if (!dateFilter || !dateFilter.value || !Array.isArray(dateFilter.value)) {
                    return new Date().toLocaleDateString('tr-TR');
                  }
                  const [startDate, endDate] = dateFilter.value;
                  if (!startDate || !endDate) {
                    return new Date().toLocaleDateString('tr-TR');
                  }
                  const start = new Date(startDate).toLocaleDateString('tr-TR');
                  const end = new Date(endDate).toLocaleDateString('tr-TR');
                  if (start === end) {
                    return start;
                  }
                  return `${start} - ${end}`;
                }
                for (const report of selectedReports) {
                  const reportJObj = JSON.parse(report);
                  const response = await fetchAuthClient('Report/export', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      id: reportJObj.id,
                      columnFilters: filters
                    }),
                  });
                  const blob = await response.blob();
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = reportJObj.name.replace(" ", "_") + "_" + getDateRangeTextFromFilters(filters) + '.xlsx';
                  document.body.appendChild(link);
                  link.click();
                  link.remove();
                  URL.revokeObjectURL(url);
                }
                notifications.hide('export-process');
                notifications.show({
                  title: 'Tüm Raporlar Tamamlandı',
                  message: `${selectedReports.length} rapor başarıyla oluşturuldu ve indirildi.`,
                  color: 'green',
                  autoClose: 5000,
                });
              } catch (error) {
                console.error('Veri çekme hatası:', error);
                notifications.hide('export-process');
                notifications.show({
                  title: 'Hata',
                  message: error.message || 'Raporlar oluşturulurken bir hata oluştu.',
                  color: 'red',
                  autoClose: 7000,
                });
              } finally {
                setLoading(false);
                setIsExporting(false);
              }
            }}
          >
            Seçili Raporları Oluştur ({selectedReports.length})
          </Button>
        </Group>
      </Card>
      <Stack gap="xl">
        <Card p="xl" radius="md" withBorder>
          <Checkbox.Group value={selectedReports} onChange={setSelectedReports}>
            <SimpleGrid cols={{ base: 1, sm: 1, md: 2, lg: 2 }} spacing="lg" verticalSpacing="lg">
              {reports.map((item, index) => {
                return (
                  <Checkbox.Card
                    className={classes.root}
                    radius="lg"
                    key={index}
                    value={JSON.stringify(item)}
                    style={{
                      border: selectedReports.includes(item.id)
                        ? `2px solid var(--mantine-color-green-6)`
                        : undefined,
                      transition: 'all 0.2s ease',
                    }}
                  >
                    <Group wrap="nowrap" align="flex-start" gap="md">
                      <div style={{ paddingTop: 4 }}>
                        <Checkbox.Indicator />
                      </div>
                      <Stack gap="xs" style={{ flex: 1 }}>
                        <Text className={classes.label} fw={600} size="md">
                          {item.name}
                        </Text>
                        <Text className={classes.description} size="sm" c="dimmed" lineClamp={2}>
                          {item.description}
                        </Text>
                      </Stack>
                      <div>
                        {permissions.includes("Report.Update") && (
                          <ActionIcon onClick={() => {
                            setEditedReport(item);
                            open();
                          }} variant="subtle" aria-label="Düzenle">
                            <IconEdit />
                          </ActionIcon>
                        )}
                        {permissions.includes("Report.Delete") && (
                          <ActionIcon onClick={() => {
                            modals.openConfirmModal({
                              title: 'Delete your profile',
                              centered: true,
                              children: (
                                <Text size="sm">
                                  Bu işlem geri alınamaz, silme işlemine devam etmek istiyor musunuz?
                                </Text>
                              ),
                              labels: { confirm: 'Sil', cancel: "İptal" },
                              confirmProps: { color: 'red' },
                              onConfirm: async () => {
                                handleFetchSubmit(
                                  fetchAuthClient('Report/delete/' + item.id, {
                                    method: 'POST'
                                  }),
                                  async (response) => {
                                    setEditedReport(undefined);
                                    setSelectedReports([])
                                    const response2 = await fetchAuthClient(`Report`, {
                                      method: 'GET',
                                    });
                                    const data = await response2.json();
                                    setReports(data)
                                  }
                                );
                              },
                            });
                          }} variant="subtle" color="red" aria-label="Sil">
                            <IconTrash />
                          </ActionIcon>
                        )}
                      </div>
                    </Group>
                  </Checkbox.Card>
                );
              })}
            </SimpleGrid>
          </Checkbox.Group>
        </Card>
        {selectedReports.length > 0 && (
          <Paper p="md" radius="md" bg="blue.0" style={{ border: '1px solid var(--mantine-color-blue-3)' }}>
            <Group>
              <ThemeIcon color="blue" variant="light">
                <IconDownload size={16} />
              </ThemeIcon>
              <Text size="sm" fw={500}>
                {selectedReports.length} rapor seçildi. İndirmek için yukarıdaki &quot;Seçili Raporları Oluştur&quot;
                butonuna tıklayın.
              </Text>
            </Group>
          </Paper>
        )}
        <PivotModal
          opened={opened}
          filters={filters}
          onClose={async () => {
            close();
            setEditedReport(undefined);
            setSelectedReports([])
            const response = await fetchAuthClient(`Report`, {
              method: 'GET',
            });
            const data = await response.json();
            setReports(data)
          }}
          editedReportProp={editedReport}
        />
      </Stack>
    </Container>
  );
}
