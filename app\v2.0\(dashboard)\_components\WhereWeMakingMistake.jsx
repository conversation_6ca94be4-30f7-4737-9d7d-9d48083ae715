import { Box, Card, Group, Table, Text, ThemeIcon } from '@mantine/core';
import React from 'react';

const WhereWeMakingMistake = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ paddingBottom: '8px', maxHeight: "350px", overflowY: "scroll" }}>
      <Text c="black" size="xl" tt="uppercase" fw={700}>
        NEREDE HATA YAPIYORUZ
      </Text>
      <>
        {(() => {
          const combinedQualityRulesName = Object.keys(dashboardDataCall.promptFailDict).concat(
            Object.keys(dashboardDataChat.promptFailDict)
          );
          const qualityRulesName = combinedQualityRulesName.filter((item, idx) => {
            return combinedQualityRulesName.indexOf(item) === idx;
          });
          const totalQualityRules = [];
          qualityRulesName.forEach((name) => {
            totalQualityRules.push({
              name,
              Çağrı: dashboardDataCall.promptFailDict[name] ?? 0,
              Yazışma: dashboardDataChat.promptFailDict[name] ?? 0,
              total: (dashboardDataCall.promptFailDict[name] ?? 0) + (dashboardDataChat.promptFailDict[name] ?? 0),
            });
          });
          let qualityRuleTotal = 0;
          let qualityRuleCagriTotal = 0;
          let qualityRuleChatTotal = 0;
          totalQualityRules.forEach((x) => {
            qualityRuleTotal += x.total;
            qualityRuleCagriTotal += x.Çağrı;
            qualityRuleChatTotal += x.Yazışma;
          });
          return (
            <>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: '300px' }}>Hata</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Çağrı</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Yazışma</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {totalQualityRules
                    .sort((a, b) => b.total - a.total)
                    .map((x) => (
                      <Table.Tr key={x.name}>
                        <Table.Td style={{ width: '300px' }}>
                          <Group spacing="xs" style={{ display: 'flex', alignItems: 'center' }}>
                            <div className="flex items-center space-x-4">
                              <ThemeIcon color="red" size={8} radius="xl" />
                              <Text fw={500}>{x.name}</Text>
                            </div>
                          </Group>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#1971c2',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Call',
                                  analysis: x.name,
                                });
                              }
                            }}
                          >
                            {(dashboardDataCall.promptFailDict[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#ff9500',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Chat',
                                  analysis: x.name,
                                });
                              }
                            }}
                          >
                            {(dashboardDataChat.promptFailDict[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                </Table.Tbody>
              </Table>
            </>
          );
        })()}
      </>
    </Card>
  );
};

export default WhereWeMakingMistake;
