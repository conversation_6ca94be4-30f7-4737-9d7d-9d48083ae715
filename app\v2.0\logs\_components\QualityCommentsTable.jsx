'use client';

import React, { useState, useEffect } from 'react';
import { Card, Group, Button, Text, Alert, Tabs, Table, Badge, Tooltip, ScrollArea } from '@mantine/core';
import { IconFileExport, IconAlertCircle, IconMessageDots, IconPhone } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import ExcelJS from 'exceljs';

// eslint-disable-next-line react/prop-types
const QualityCommentsTable = ({ filters, fetchAuthClient }) => {
  const [callComments, setCallComments] = useState([]);
  const [chatComments, setChatComments] = useState([]);
  const [error, setError] = useState(null);
  const [activeSubTab, setActiveSubTab] = useState('call');

  const fetchComments = async () => {
    setError(null);

    try {
      const [callResponse, chatResponse] = await Promise.all([
        fetchAuthClient('Call/qualityComment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(filters),
        }),
        fetchAuthClient('Chat/qualityComment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(filters),
        }),
      ]);

      if (callResponse.ok) {
        const callData = await callResponse.json();
        setCallComments(Array.isArray(callData) ? callData : callData.data || []);
      } else {
        console.error('Call comments fetch failed:', callResponse.status);
      }

      if (chatResponse.ok) {
        const chatData = await chatResponse.json();
        setChatComments(Array.isArray(chatData) ? chatData : chatData.data || []);
      } else {
        console.error('Chat comments fetch failed:', chatResponse.status);
      }
    } catch (err) {
      console.error('Error fetching comments:', err);
      setError('Yorumlar yüklenirken bir hata oluştu.');
    }
  };

  useEffect(() => {
    fetchComments();
  }, [filters]);

  const exportToExcel = async (type = 'all') => {
    try {
      let dataToExport = [];
      let fileName = '';

      if (type === 'call') {
        dataToExport = callComments;
        fileName = 'Cagri_Kalite_Yorumlari';
      } else if (type === 'chat') {
        dataToExport = chatComments;
        fileName = 'Yazisma_Kalite_Yorumlari';
      } else {
        dataToExport = [
          ...callComments.map((c) => ({ ...c, type: 'Çağrı' })),
          ...chatComments.map((c) => ({ ...c, type: 'Yazışma' })),
        ];
        fileName = 'Tum_Kalite_Yorumlari';
      }

      if (dataToExport.length === 0) {
        notifications.show({
          title: 'Uyarı',
          message: 'Dışa aktarılacak veri bulunamadı.',
          color: 'orange',
        });
        return;
      }

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Kalite Yorumları');

      const headers = [
        'Identifier',
        'Yorum Tarihi',
        'Kayıt Tarihi',
        'Kullanıcı ID',
        'Kullanıcı Adı',
        'Yorum',
        'Etiketler',
      ];

      if (type === 'all') {
        headers.unshift('Tip');
      }

      worksheet.addRow(headers);

      dataToExport.forEach((comment) => {
        const row = [
          comment.identifier || '',
          comment.date ? new Date(comment.date).toLocaleString('tr-TR') : '',
          comment.recordDate ? new Date(comment.recordDate).toLocaleString('tr-TR') : '',
          comment.userId || '',
          comment.userName || '',
          comment.comment || '',
          comment.tags || '',
        ];

        if (type === 'all') {
          row.unshift(comment.type || '');
        }

        worksheet.addRow(row);
      });

      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6E6FA' },
      };

      worksheet.columns.forEach((column) => {
        let maxLength = 0;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const dateStr = new Date().toLocaleDateString('tr-TR').replace(/\//g, '_');
      link.download = `${fileName}_${dateStr}.xlsx`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notifications.show({
        title: 'Başarılı',
        message: 'Excel dosyası başarıyla indirildi.',
        color: 'green',
      });
    } catch (err) {
      console.error('Excel export error:', err);
      notifications.show({
        title: 'Hata',
        message: 'Excel dosyası oluşturulurken bir hata oluştu.',
        color: 'red',
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const dateStr = date.toLocaleDateString('tr-TR');
    const timeStr = date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    });
    return (
      <div style={{ lineHeight: 1.2 }}>
        <div>{dateStr}</div>
        <div style={{ fontSize: '0.85em', color: '#666' }}>{timeStr}</div>
      </div>
    );
  };

  const renderTags = (tags) => {
    if (!tags) return null;

    const tagList = tags
      .split(',')
      .map((tag) => tag.trim())
      .filter(Boolean);
    if (tagList.length === 0) return null;

    const firstTag = tagList[0];
    const remainingTags = tagList.slice(1);
    const remainingCount = remainingTags.length;

    return (
      <Group gap={4} wrap="nowrap">
        <Text size="sm" c="blue" fw={500}>
          {firstTag}
        </Text>
        {remainingCount > 0 && (
          <Tooltip
            label={
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                {remainingTags.map((tag, index) => (
                  <div key={index} style={{ padding: '2px 0' }}>
                    {tag}
                  </div>
                ))}
              </div>
            }
            position="top"
          >
            <Badge size="lg" variant="filled" color="gray">
              <div className="flex justify-center items-center mx-1">+{remainingCount}</div>
            </Badge>
          </Tooltip>
        )}
      </Group>
    );
  };

  const renderCommentsTable = (comments) => {
    return (
      <ScrollArea>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th w={150}>Identifier</Table.Th>
              <Table.Th w={120}>Yorum Tarihi</Table.Th>
              <Table.Th w={120}>Kayıt Tarihi</Table.Th>
              <Table.Th w={150}>Kullanıcı</Table.Th>
              <Table.Th w={300}>Yorum</Table.Th>
              <Table.Th w={200}>Etiketler</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {comments.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={6} ta="center" py="xl">
                  <Text c="dimmed">Gösterilecek yorum bulunmuyor.</Text>
                </Table.Td>
              </Table.Tr>
            ) : (
              comments.map((comment, index) => (
                <Table.Tr key={index}>
                  <Table.Td>
                    <Tooltip label={comment.identifier} position="top-start">
                      <Text
                        size="sm"
                        style={{
                          maxWidth: '130px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {comment.identifier}
                      </Text>
                    </Tooltip>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{formatDate(comment.date)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{formatDate(comment.recordDate)}</Text>
                  </Table.Td>
                  <Table.Td>
                    <div>
                      <Text size="sm" fw={500}>
                        {comment.userName}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <ScrollArea h={80}>
                      <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
                        {comment.comment}
                      </Text>
                    </ScrollArea>
                  </Table.Td>
                  <Table.Td>{renderTags(comment.tags)}</Table.Td>
                </Table.Tr>
              ))
            )}
          </Table.Tbody>
        </Table>
      </ScrollArea>
    );
  };

  return (
    <Card withBorder>
      <Group justify="space-between" mb="md">
        <Text size="lg" fw={500}>
          Kalite Yorumları
        </Text>
        <Group>
          <Button
            leftSection={<IconFileExport size={16} />}
            onClick={() => exportToExcel('all')}
            variant="filled"
            size="sm"
          >
            Tümünü Dışa Aktar
          </Button>
          <Button onClick={fetchComments} variant="light" size="sm">
            Yenile
          </Button>
        </Group>
      </Group>

      {error && (
        <Alert icon={<IconAlertCircle size={16} />} title="Hata" color="red" mb="md">
          {error}
        </Alert>
      )}

      <Tabs value={activeSubTab} onChange={setActiveSubTab}>
        <Tabs.List>
          <Tabs.Tab value="call" leftSection={<IconPhone size={16} />}>
            Çağrı Yorumları ({callComments.length})
          </Tabs.Tab>
          <Tabs.Tab value="chat" leftSection={<IconMessageDots size={16} />}>
            Yazışma Yorumları ({chatComments.length})
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="call" pt="md">
          <Group justify="flex-end" mb="sm">
            <Button
              leftSection={<IconFileExport size={14} />}
              onClick={() => exportToExcel('call')}
              variant="light"
              size="xs"
            >
              Çağrı Yorumlarını Dışa Aktar
            </Button>
          </Group>
          {renderCommentsTable(callComments)}
        </Tabs.Panel>

        <Tabs.Panel value="chat" pt="md">
          <Group justify="flex-end" mb="sm">
            <Button
              leftSection={<IconFileExport size={14} />}
              onClick={() => exportToExcel('chat')}
              variant="light"
              size="xs"
            >
              Yazışma Yorumlarını Dışa Aktar
            </Button>
          </Group>
          {renderCommentsTable(chatComments)}
        </Tabs.Panel>
      </Tabs>
    </Card>
  );
};

export default QualityCommentsTable;
