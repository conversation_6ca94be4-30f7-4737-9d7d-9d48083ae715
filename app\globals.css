@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.cursor-pointer:hover {
  cursor: pointer;
}

.m_847eaf.mantine-ChartLegend-legend {
  justify-content: center !important;
}

.button-container {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 10px;
  width: 100%;
  display: flex;
  justify-content: center; /* Butonu yatay olarak ortalar */
  align-items: center; /* <PERSON><PERSON> olarak ortalar */
  z-index: 10;
  border-top: none; /* Kartın üst çizgisini kaldırır */
  box-shadow: none; /* Kart gölgesini kaldırır */
}

.button-container button {
  border: none; /* Kenarlıkları kaldır */
  background: none; /* Arka planı kaldır */
  color: #007bff; /* Link rengi gibi görünmesini sağla */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline; /* Altı çizili yap */
}

.button-container button:hover {
  text-decoration: none; /* Üzerine gelince alt çizgiyi kaldır */
}

/* Dashboard container */
.dashboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 20px;
  overflow: hidden;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  text-align: left;
  word-wrap: break-word;
  margin-bottom: 10px;
}

.card-body {
  flex-grow: 1;
  overflow: hidden;
  padding: 0;
}

.card-body .image-container {
  text-align: center;
}

.card-body h6 {
  font-size: 1rem;
}

.card-body h4 {
  font-size: 1.5rem;
}

.card-item {
  flex: 1 1 calc(20% - 20px);
  max-width: 400px;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

@media (max-width: 1200px) {
  .card-title {
    font-size: 1rem;
  }

  .card-item {
    flex: 1 1 calc(25% - 20px);
  }

  .card-body h6 {
    font-size: 0.9rem;
  }

  .card-body h4 {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .card-title {
    font-size: 0.9rem;
  }
  .card-item {
    flex: 1 1 calc(33.33% - 20px);
  }

  .card-body h6 {
    font-size: 0.8rem;
  }

  .card-body h4 {
    font-size: 1rem;
  }

  .image-container img {
    max-width: 100%;
    height: auto;
  }
}

.shepherd-theme-custom {
  background: linear-gradient(135deg, #1e293b, #334155) !important;
  color: #ffffff !important;
  border-radius: 12px !important;
  padding: 20px !important;
  border: 1px solid #4b5563 !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5) !important;
}

/* Shepherd Başlık Arkaplanını Kaldırdıgk */
.shepherd-theme-custom .shepherd-header {
  background: transparent !important;
  border-bottom: none !important;
  padding: 0 !important;
}

.shepherd-theme-custom .shepherd-title {
  background: transparent !important;
  color: #facc15 !important;
  font-size: 20px !important;
  font-weight: bold !important;
  text-align: center !important;
  margin-bottom: 10px !important;
  padding: 5px 10px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.shepherd-theme-custom .shepherd-title svg {
  width: 20px !important;
  height: 20px !important;
}

.shepherd-theme-custom .shepherd-text {
  font-size: 15px !important;
  color: #e2e8f0 !important;
  text-align: center !important;
  line-height: 1.5 !important;
}

.shepherd-theme-custom .shepherd-button {
  background-color: #6366f1 !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: background 0.3s ease-in-out !important;
  border: none !important;
  outline: none !important;
  display: inline-block !important;
}

.shepherd-theme-custom .shepherd-button:hover {
  background-color: #4f46e5 !important;
  transform: scale(1.05) !important;
}

.shepherd-theme-custom .shepherd-button:nth-child(1) {
  background-color: #64748b !important;
}

.shepherd-theme-custom .shepherd-button:nth-child(1):hover {
  background-color: #475569 !important;
}

.shepherd-theme-custom .shepherd-cancel-icon {
  color: #f87171 !important;
  font-size: 20px !important;
  position: absolute !important;
  top: 12px !important;
  right: 15px !important;
  cursor: pointer !important;
  transition: color 0.2s ease-in-out !important;
}

.shepherd-theme-custom .shepherd-cancel-icon:hover {
  color: #dc2626 !important;
}

.shepherd-theme-custom .shepherd-arrow {
  display: none !important;
}

/* Shepherd Pop-up Açılma Efekti */
.shepherd-theme-custom {
  animation: fadeInUp 0.4s ease-in-out !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}



/* PDF Export Styles */
@media print {
  #pdf-content {
    width: 1200px !important;
    max-width: 1200px !important;
    min-width: 1200px !important;
    transform: scale(1) !important;
    transform-origin: top left !important;
  }

  #pdf-content .mantine-Grid-root {
    width: 100% !important;
  }

  #pdf-content .mantine-Grid-col {
    flex: none !important;
  }
}

/* PDF Content Container Styles */
#pdf-content {
  background: white !important;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

#pdf-content * {
  box-sizing: border-box;
}

/* PDF için chart ve grafik optimizasyonları */
#pdf-content .recharts-wrapper {
  background: white !important;
}

#pdf-content .mantine-Card-root {
  background: white !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* PDF için text optimizasyonları */
#pdf-content .mantine-Text-root {
  color: #333 !important;
}

#pdf-content .mantine-Title-root {
  color: #333 !important;
}

/* Responsive adjustments for PDF content */
@media (min-width: 1400px) {
  #pdf-content {
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* PDF için grid optimizasyonları */
#pdf-content .mantine-Grid-root {
  margin: 0 !important;
}

#pdf-content .mantine-Grid-col {
  padding: 8px !important;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
.react-flow__node {
    border: none !important;
  box-shadow: none !important;
}
