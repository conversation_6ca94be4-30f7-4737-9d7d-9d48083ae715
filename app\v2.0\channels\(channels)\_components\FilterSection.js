'use client';
import React from 'react';
import { Box, Button, Group } from '@mantine/core';
import {
  IconAlarm,
  IconBan,
  IconBuilding,
  IconCategory,
  IconCategory2,
  IconCheck,
  IconClock,
  IconHash,
  IconKey,
  IconLanguage,
  IconMoodSmile,
  IconPhoneCall,
  IconPoint,
  IconRuler,
  IconSearch,
  IconServer,
  IconUser,
  IconVolume,
  IconVolumeOff,
  IconX,
} from '@tabler/icons-react';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import TextFilterPopover from '@/common/components/filters/TextFilterPopover';
import NumberRangeFilterPopover from '@/common/components/filters/NumberRangeFilterPopover';
import TagFilterPopover from '@/common/components/filters/TagFilterPopover';
import GroupedSelectFilterPopover from '@/common/components/filters/GroupedSelectFilterPopover';

export default function FilterSection({
  user,
  channelType,
  clearFilters,
  setFilter,
  initDateType,
  providerFilterValue,
  setProviderFilterValue,
  dateFilterValue,
  setDateFilterValue,
  isAnalysisCompletedFilterValue,
  setIsAnalysisCompletedFilterValue,
  agentIdFilterValue,
  setAgentIdFilterValue,
  agents,
  detailedFiltersVisible,
  setDetailedFiltersVisible,
  alarmCategoriesFilterValue,
  setAlarmCategoriesFilterValue,
  identifierFilterValue,
  setIdentifierFilterValue,
  categoryFilterValue,
  setCategoryFilterValue,
  subcategoryFilterValue,
  setSubcategoryFilterValue,
  pointFilterValue,
  setPointFilterValue,
  durationFilterValue,
  setDurationFilterValue,
  maxSlienceFilterValue,
  setMaxSlienceFilterValue,
  typeFilterValue,
  setTypeFilterValue,
  analysisFilterValue,
  setAnalysisFilterValue,
  specialFilterFilterValue,
  setSpecialFilterFilterValue,
  languageFilterValue,
  setLanguageFilterValue,
  contactTypeFilterValue,
  setContactTypeFilterValue,
  issueStatusFilterValue,
  setIssueStatusFilterValue,
  sentimentFilterValue,
  setSentimentFilterValue,
  keywordsFilterValue,
  setKeywordsFilterValue,
  vendorFilterValue,
  setVendorFilterValue,
  tenantParameters,
}) {
  return (
    <Group>
      <Button
        color="red"
        variant={'light'}
        onClick={clearFilters}
        leftSection={<IconX size={16} />}
        style={{ marginRight: 10 }}
      >
        Filtreleri Temizle
      </Button>
      {user.role === 'ADMİN' && (
        <SelectFilterPopover
          value={providerFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setProviderFilterValue, 'provider')}
          icon={<IconServer />}
          label={'Sağlayıcı'}
          data={
            channelType === 'Call'
              ? [
                { value: 'oprisus', label: 'Oprisus' },
                { value: 'sirius', label: 'Sirius' },
                { value: 'democall', label: 'Demo Arama' },
                { value: 'manuel', label: 'Manuel' },
              ]
              : channelType === 'Chat'
                ? [
                  { value: 'insider', label: 'Insider' },
                  { value: 'manuel', label: 'Manuel' },
                ]
                : []
          }
        />
      )}
      <DateFilterPopover
        value={dateFilterValue}
        onChange={(value, isEmpty) => setFilter(value, isEmpty, setDateFilterValue, 'date')}
        label={'Tarih Aralığı'}
        initDateTypeValue={initDateType}
      />
      <SelectFilterPopover
        value={isAnalysisCompletedFilterValue}
        onChange={(value, isEmpty) =>
          setFilter(value, isEmpty, setIsAnalysisCompletedFilterValue, 'isAnalysisCompleted')
        }
        icon={<IconCheck />}
        label={'Analiz Durumu'}
        data={[
          { value: 'true', label: 'Tamamlandı' },
          { value: 'false', label: 'Tamamlanmadı' },
        ]}
      />
      {(user.role !== "ÇAĞRI MT" && user.role !== "YAZIŞMA MT") && (
        <>
          <GroupedSelectFilterPopover
            label="Temsilci"
            icon={<IconUser />}
            value={agentIdFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setAgentIdFilterValue, 'agentId')}
            groupedData={[
              {
                group: 'Aktif Temsilciler',
                options: agents.filter(user => user.extraJson?.IsActive === true).map(user => ({
                  value: '' + user.id,
                  label: `${user.name} ${user.surname}`,
                })),
              },
              {
                group: 'Pasif Temsilciler',
                options: agents.filter(user => user.extraJson?.IsActive === false).map(user => ({
                  value: '' + user.id,
                  label: `${user.name} ${user.surname}`,
                })),
              },
            ]}
            multiple
          />
          <SelectFilterPopover
            value={vendorFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setVendorFilterValue, 'vendor')}
            icon={<IconBuilding />}
            label={'Şirketler'}
            data={
              tenantParameters.vendors
                ? tenantParameters.vendors.map((vendor) => ({
                  value: vendor,
                  label: vendor,
                }))
                : []
            }
          />
        </>
      )}
      <Button
        variant={detailedFiltersVisible ? 'filled' : 'outline'}
        color="gray"
        onClick={() => setDetailedFiltersVisible(!detailedFiltersVisible)}
        leftSection={detailedFiltersVisible ? <IconX size={16} /> : <IconSearch size={16} />}
      >
        {detailedFiltersVisible ? 'Detaylı Aramayı Kapat' : 'Detaylı Arama'}
      </Button>
      <Box
        style={{
          display: detailedFiltersVisible ? 'flex' : 'none',
          flexWrap: 'wrap',
          gap: '8px',
          width: '100%',
          transition: 'all 0.3s ease',
          marginTop: detailedFiltersVisible ? '10px' : '0',
          overflow: 'hidden',
          maxHeight: detailedFiltersVisible ? '500px' : '0',
        }}
      >
        <SelectFilterPopover
          multiple
          value={alarmCategoriesFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setAlarmCategoriesFilterValue, 'alarmCategories')}
          icon={<IconAlarm />}
          label={'Alarm Kategorileri'}
          data={[
            { value: 'CİMER / Devlet Şikayeti', label: 'CİMER / Devlet Şikayeti' },
            { value: 'Mahkeme / Yasal Tehdit', label: 'Mahkeme / Yasal Tehdit' },
            { value: 'Şikayetvar / Dış Platformlar', label: 'Şikayetvar / Dış Platformlar' },
            { value: 'Sosyal Medya Şikayeti / İfşa', label: 'Sosyal Medya Şikayeti / İfşa' },
            { value: 'Erişim Sorunu', label: 'Erişim Sorunu' },
            {
              value: 'KVKK / Güvenlik / Dolandırıcılık İddiası',
              label: 'KVKK / Güvenlik / Dolandırıcılık İddiası',
            },
            { value: 'İletişim Sorunu / Temsilciyle Tartışma', label: 'İletişim Sorunu / Temsilciyle Tartışma' },
            { value: 'Agent Hakaret / Küfür / Aşağılama', label: 'Agent Hakaret / Küfür / Aşağılama' },
            { value: 'Müşteri Hakaret / Küfür / Aşağılama', label: 'Müşteri Hakaret / Küfür / Aşağılama' },
            { value: 'Kritik Operasyonel Sorunlar', label: 'Kritik Operasyonel Sorunlar' },
            { value: 'Tüketici Hakem Heyeti / BTK', label: 'Tüketici Hakem Heyeti / BTK' },
          ]}
        />
        <TextFilterPopover
          multiple
          value={identifierFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setIdentifierFilterValue, 'identifier')}
          icon={<IconKey />}
          label={'ID'}
        />
        <SelectFilterPopover
          multiple
          value={categoryFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setCategoryFilterValue, 'category')}
          icon={<IconCategory />}
          label={'Ana Kategori'}
          data={Object.keys(tenantParameters.categories).map((x) => ({
            value: x,
            label: x,
          }))}
        />
        <SelectFilterPopover
          multiple
          value={subcategoryFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setSubcategoryFilterValue, 'subCategory')}
          icon={<IconCategory2 />}
          label={'Alt Kategori'}
          data={Object.values(tenantParameters.categories)
            .flat()
            .map((x) => ({
              value: x,
              label: x,
            }))}
        />
        <NumberRangeFilterPopover
          value={pointFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setPointFilterValue, 'point')}
          min={0}
          max={100}
          icon={<IconPoint />}
          label={'Puan'}
        />
        <NumberRangeFilterPopover
          value={durationFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setDurationFilterValue, 'duration')}
          min={0}
          max={10000}
          icon={<IconClock />}
          label={'Süre'}
        />
        <NumberRangeFilterPopover
          value={maxSlienceFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setMaxSlienceFilterValue, 'maxSlience')}
          min={0}
          max={10000}
          icon={<IconVolumeOff />}
          label={'Sessizlik'}
        />
        {(user.role === 'ADMİN' || channelType === 'Call') && (
          <SelectFilterPopover
            label="Kayıt Tipi"
            icon={<IconVolume />}
            value={typeFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setTypeFilterValue, 'type')}
            data={[
              { value: 'mono', label: 'Mono (Tek Kanal)' },
              { value: 'stereo', label: 'Stereo (Çift Kanal)' },
            ]}
          />
        )}
        <SelectFilterPopover
          label="Kalite Kural Hatası"
          multiple
          icon={<IconRuler />}
          value={analysisFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setAnalysisFilterValue, 'analysis')}
          data={tenantParameters.qualityRules.map((x) => ({
            value: x,
            label: x,
          }))}
        />
        <SelectFilterPopover
          label="Alarm"
          multiple
          icon={<IconAlarm />}
          value={specialFilterFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setSpecialFilterFilterValue, 'specialFilter')}
          data={[
            { value: 'agent_kufur', label: 'Agent Küfür' },
            { value: 'agent_hakaret', label: 'Agent Hakaret' },
            { value: 'customer_kufur', label: 'Müşteri Küfür' },
            { value: 'customer_hakaret', label: 'Müşteri Hakaret' },
            { value: 'agent_kufur_hakaret', label: 'Agent Küfür & Hakaret' },
            { value: 'customer_kufur_hakaret', label: 'Müşteri Küfür & Hakaret' },
            { value: 'bannedWord', label: 'Yasaklı Kelime' },
          ]}
        />
        <SelectFilterPopover
          label="Dil"
          icon={<IconLanguage />}
          value={languageFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setLanguageFilterValue, 'language')}
          data={[{ value: 'tr', label: 'Türkçe' }]}
        />
        <SelectFilterPopover
          label="İletişim Türü"
          multiple
          icon={<IconPhoneCall />}
          value={contactTypeFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setContactTypeFilterValue, 'contactType')}
          data={[
            { value: 'Destek Talebi', label: 'Destek Talebi' },
            { value: 'Bilgi Talebi', label: 'Bilgi Talebi' },
            { value: 'Teşekkür', label: 'Teşekkür' },
            { value: 'Öneri', label: 'Öneri' },
            { value: 'Şikayet', label: 'Şikayet' },
            { value: 'Talep', label: 'Talep' },
            { value: 'Geri Bildirim', label: 'Geri Bildirim' },
            { value: 'Diğer', label: 'Diğer' },
          ]}
        />
        <SelectFilterPopover
          label="Sorun Durumu"
          icon={<IconBan />}
          value={issueStatusFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setIssueStatusFilterValue, 'issueStatus')}
          data={[
            { value: 'Bilgi Talebi', label: 'Bilgi Talebi' },
            { value: 'Müşterinin Problemi Vardı, Çözüldü', label: 'Müşterinin Problemi Vardı, Çözüldü' },
            { value: 'Müşterinin Problemi Vardı, Çözülmedi', label: 'Müşterinin Problemi Vardı, Çözülmedi' },
          ]}
        />
        <SelectFilterPopover
          multiple
          label="Duygu Durumu"
          icon={<IconMoodSmile />}
          value={sentimentFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setSentimentFilterValue, 'sentiment')}
          data={[
            { value: 'positive', label: 'Memnuniyet' },
            { value: 'negative', label: 'Hayal Kırıklığı' },
            { value: 'notr', label: 'Nötr' },
          ]}
        />
        <TagFilterPopover
          label="Anahtar Kelime"
          icon={<IconHash />}
          value={keywordsFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, setKeywordsFilterValue, 'keywords')}
        />
      </Box>
    </Group>
  );
}
