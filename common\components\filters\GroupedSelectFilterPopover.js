'use client';
import React, { useState } from 'react';
import {
    ActionIcon,
    Button,
    Popover,
    Stack,
    Checkbox,
    Radio,
    Text,
    Group,
    ScrollArea,
    Accordion
} from '@mantine/core';
import { IconX } from '@tabler/icons-react';

const GroupedSelectFilterPopover = ({
    label,
    value,
    onChange,
    groupedData,
    icon,
    multiple = true,
}) => {
    const [opened, setOpened] = useState(false);
    const [tempValue, setTempValue] = useState(
        value !== undefined ? value : multiple ? [] : null
    );
    const isEmptyValue = (val) => (multiple ? val.length === 0 : val === null);

    const handleToggle = (val) => {
        if (multiple) {
            if (tempValue.includes(val)) {
                setTempValue(tempValue.filter((v) => v !== val));
            } else {
                setTempValue([...tempValue, val]);
            }
        } else {
            setTempValue(val);
        }
    };

    const handleSelectAll = () => {
        const all = groupedData.flatMap((g) => g.options.map((o) => o.value));
        setTempValue(all);
    };

    const handleClearAll = () => {
        setTempValue([]);
    };

    const isAllSelected =
        multiple &&
        tempValue.length === groupedData.flatMap((g) => g.options.map((o) => o.value)).length;

    const isGroupChecked = (groupOptions) =>
        groupOptions.every((o) => tempValue.includes(o.value));

    const isGroupIndeterminate = (groupOptions) =>
        groupOptions.some((o) => tempValue.includes(o.value)) &&
        !isGroupChecked(groupOptions);

    const toggleGroup = (groupOptions) => {
        const allValues = groupOptions.map((o) => o.value);
        if (isGroupChecked(groupOptions)) {
            setTempValue(tempValue.filter((v) => !allValues.includes(v)));
        } else {
            setTempValue([...new Set([...tempValue, ...allValues])]);
        }
    };

    return (
        <Group>
            <Popover opened={opened} onChange={setOpened} position="bottom-start" withArrow shadow="md">
                <Popover.Target>
                    <Button
                        leftSection={icon}
                        size="xs"
                        variant="light"
                        radius="xl"
                        color={isEmptyValue(value) ? 'black' : 'teal'}
                        onClick={() => {
                            setOpened(!opened);
                            setTempValue(value !== undefined ? value : multiple ? [] : null);
                        }}
                    >
                        {isEmptyValue(value)
                            ? label
                            : multiple
                                ? `${label} (${value.length})`
                                : groupedData
                                    .flatMap((g) => g.options)
                                    .find((o) => o.value === value)?.label || label}
                    </Button>
                </Popover.Target>
                <Popover.Dropdown style={{ width: 320 }}>
                    <div style={{ display: 'flex', width: '100%' }}>
                        <ActionIcon
                            style={{ marginLeft: 'auto' }}
                            variant="subtle"
                            color="black"
                            onClick={() => {
                                setOpened(false);
                                setTempValue(value);
                            }}
                        >
                            <IconX />
                        </ActionIcon>
                    </div>
                    {multiple && (
                        <Group spacing="xs" mb="sm">
                            <Button size="xs" variant="light" onClick={isAllSelected ? handleClearAll : handleSelectAll}>
                                {isAllSelected ? 'Tümünü Kaldır' : 'Tümünü Seç'}
                            </Button>
                        </Group>
                    )}
                    <ScrollArea h={250} type="auto">
                        <Accordion variant="contained" defaultValue={groupedData[0]?.group}>
                            {groupedData.map((group) => (
                                <Accordion.Item key={group.group} value={group.group}>
                                    <Accordion.Control>
                                        <Group justify="space-between" w="100%" pr={8}>
                                            <Text>{group.group}</Text>
                                            {multiple && (
                                                <Checkbox
                                                    size="xs"
                                                    checked={isGroupChecked(group.options)}
                                                    indeterminate={isGroupIndeterminate(group.options)}
                                                    onChange={() => toggleGroup(group.options)}
                                                    onClick={(e) => e.stopPropagation()}
                                                />
                                            )}
                                        </Group>
                                    </Accordion.Control>
                                    <Accordion.Panel>
                                        <Stack spacing={4} mt="xs">
                                            {group.options.map((opt) =>
                                                multiple ? (
                                                    <Checkbox
                                                        key={opt.value}
                                                        label={opt.label}
                                                        size="xs"
                                                        checked={tempValue.includes(opt.value)}
                                                        onChange={() => handleToggle(opt.value)}
                                                    />
                                                ) : (
                                                    <Radio
                                                        key={opt.value}
                                                        label={opt.label}
                                                        size="xs"
                                                        value={opt.value}
                                                        checked={tempValue === opt.value}
                                                        onChange={() => handleToggle(opt.value)}
                                                    />
                                                )
                                            )}
                                        </Stack>
                                    </Accordion.Panel>
                                </Accordion.Item>
                            ))}
                        </Accordion>
                    </ScrollArea>
                    <Button
                        mt="sm"
                        size="xs"
                        fullWidth
                        onClick={() => {
                            onChange(tempValue, isEmptyValue(tempValue));
                            setOpened(false);
                        }}
                    >
                        Filtrele
                    </Button>
                </Popover.Dropdown>
            </Popover>
            {!isEmptyValue(value) && (
                <ActionIcon
                    variant="subtle"
                    onClick={() => {
                        setTempValue(multiple ? [] : null);
                        onChange(multiple ? [] : null, true);
                        setOpened(false);
                    }}
                >
                    <IconX />
                </ActionIcon>
            )}
        </Group>
    );
};

export default GroupedSelectFilterPopover;
