'use client';
// #region imports
import React, { useContext, useEffect, useRef, useState } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { Card, Grid } from '@mantine/core';
import { useSearchParams } from 'next/navigation';
import classes from '../../StatsSegments.module.css';

import { getDatesFromType } from '@/common/functions/commonFunctions';
import {
  CategoryScale,
  Chart as ChartJS,
  Title as ChartTitle,
  Tooltip as ChartTooltip,
  Filler,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
} from 'chart.js';
import AverageQualityScore from './_components/AverageQualityScore';
import AverageSilenceDuration from './_components/AverageSilenceDuration';
import CallNumber from './_components/CallNumber';
import ChannelTable from './_components/ChannelTable';
import FilterSection from './_components/FilterSection';
import LineGraph from './_components/LineGraph';
import NumberOfMainCategories from './_components/NumberOfMainCategories';
import ProhibitedWord from './_components/ProhibitedWord';
import ViolationStats from './_components/ViolationStats';
import QualityControlNumber from './_components/QualityControlNumber';
import { getChannelTableColumns } from './_lib/getChannelTableColumns';
import { getChartJsOptions } from './_lib/getChartJsOptions';
import { formatChartData } from './chartUtils';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, ChartTitle, ChartTooltip, Legend, Filler);

export default function ChannelPage() {
  const { permissions, fetchAuthClient, user } = useContext(AuthContext);
  const { getTenantParameters, getAgents } = useContext(PluktoContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');
  const filtersBase64 = searchParams.get('filters');
  const date = getDatesFromType('lastonemonth');
  let initDateType = 'lastonemonth';
  let initFilters = [
    {
      id: 'date',
      value: date,
    },
    {
      id: 'agentId',
      value: getAgents(channelType).filter(user => user.extraJson?.IsActive === true).map(user => '' + user.id)
    }
  ];
  if (filtersBase64) {
    const binaryString = atob(filtersBase64);
    const byteArray = Uint8Array.from(binaryString, (char) => char.charCodeAt(0));
    const jsonString = new TextDecoder()
      .decode(byteArray)
      .normalize()
      .replace(/i\u0307/g, 'i');
    initFilters = JSON.parse(jsonString);
    if (initFilters.find((item) => item.id === 'date')?.value) {
      initDateType = 'manuel';
      let tempDateValue = initFilters.find((item) => item.id === 'date')?.value;
      const filtered = initFilters.filter((filter) => filter.id !== 'date');
      initFilters = [
        ...filtered,
        {
          id: 'date',
          value: [new Date(tempDateValue[0]), new Date(tempDateValue[1])],
        },
      ];
    }
    else {
      initDateType = 'manuel';
      initFilters = initFilters.filter((filter) => filter.id !== 'date');
    }
  }
  const [providerFilterValue, setProviderFilterValue] = useState(
    initFilters.find((item) => item.id === 'provider')?.value || null
  );
  const [dateFilterValue, setDateFilterValue] = useState(initFilters.find((item) => item.id === 'date')?.value || [null, null]);
  const [isAnalysisCompletedFilterValue, setIsAnalysisCompletedFilterValue] = useState(
    initFilters.find((item) => item.id === 'isAnalysisCompleted')?.value || null
  );
  const [agentIdFilterValue, setAgentIdFilterValue] = useState(
    initFilters.find((item) => item.id === 'agentId')?.value || []
  );
  const [alarmCategoriesFilterValue, setAlarmCategoriesFilterValue] = useState(
    initFilters.find((item) => item.id === 'alarmCategories')?.value || []
  );
  const [identifierFilterValue, setIdentifierFilterValue] = useState(
    initFilters.find((item) => item.id === 'identifier')?.value || ''
  );
  const [categoryFilterValue, setCategoryFilterValue] = useState(
    initFilters.find((item) => item.id === 'category')?.value || []
  );
  const [subcategoryFilterValue, setSubcategoryFilterValue] = useState(
    initFilters.find((item) => item.id === 'subCategory')?.value || []
  );
  const [durationFilterValue, setDurationFilterValue] = useState(
    initFilters.find((item) => item.id === 'duration')?.value || ['', '']
  );
  const [pointFilterValue, setPointFilterValue] = useState(
    initFilters.find((item) => item.id === 'point')?.value || ['', '']
  );
  const [maxSlienceFilterValue, setMaxSlienceFilterValue] = useState(
    initFilters.find((item) => item.id === 'maxSlience')?.value || ['', '']
  );
  const [typeFilterValue, setTypeFilterValue] = useState(initFilters.find((item) => item.id === 'type')?.value || null);
  const [analysisFilterValue, setAnalysisFilterValue] = useState(
    initFilters.find((item) => item.id === 'analysis')?.value || []
  );
  const [specialFilterFilterValue, setSpecialFilterFilterValue] = useState(
    initFilters.find((item) => item.id === 'specialFilter')?.value || []
  );
  const [languageFilterValue, setLanguageFilterValue] = useState(
    initFilters.find((item) => item.id === 'language')?.value || null
  );
  const [contactTypeFilterValue, setContactTypeFilterValue] = useState(
    initFilters.find((item) => item.id === 'contactType')?.value || []
  );
  const [issueStatusFilterValue, setIssueStatusFilterValue] = useState(
    initFilters.find((item) => item.id === 'issueStatus')?.value || null
  );
  const [sentimentFilterValue, setSentimentFilterValue] = useState(
    initFilters.find((item) => item.id === 'sentiment')?.value || []
  );
  const [keywordsFilterValue, setKeywordsFilterValue] = useState(
    initFilters.find((item) => item.id === 'keywords')?.value || []
  );
  const [vendorFilterValue, setVendorFilterValue] = useState(
    initFilters.find((item) => item.id === 'vendor')?.value || null
  );
  const [filters, setFilters] = useState(initFilters);
  const [dashboardData, setDashboardData] = useState(null);
  const [detailedFiltersVisible, setDetailedFiltersVisible] = useState(false);
  const tenantTableRef = useRef();

  if (!permissions.includes(channelType + '.View')) {
    window.location.href = '/401';
  }

  let label = '';
  if (channelType === 'Call') {
    label = 'Çağrı';
  } else if (channelType === 'Chat') {
    label = 'Yazışma';
  }

  const fetchDashboardData = async () => {
    setDashboardData(null);
    const response = await fetchAuthClient(channelType + '/dashboard', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(filters),
    });
    var responseJson = await response.json();
    setDashboardData(responseJson);
  };

  const setFilter = (value, isEmpty, setFilterMethod, filterId) => {
    setFilterMethod(value);
    setFilters((prevFilters) => {
      const filtered = prevFilters.filter((filter) => filter.id !== filterId);
      if (isEmpty) {
        return [...filtered];
      } else {
        return [
          ...filtered,
          {
            id: filterId,
            value: value,
          },
        ];
      }
    });
  };

  const handleDateClick = (event, elements) => {
    if (elements.length > 0) {
      const dataPointIndex = elements[0].index;
      const combinedData = formatChartData(dashboardData);
      if (dataPointIndex >= 0 && dataPointIndex < combinedData.length) {
        const selectedData = combinedData[dataPointIndex];
        const clickedDate = selectedData.rawDate;
        const startOfDay = new Date(clickedDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(clickedDate);
        endOfDay.setHours(23, 59, 59, 999);
        setFilter([startOfDay, endOfDay], false, setDateFilterValue, 'date');
      }
    }
  };

  const clearFilters = () => {
    setProviderFilterValue(null);
    setDateFilterValue([null, null]);
    setAgentIdFilterValue([]);
    setAlarmCategoriesFilterValue([]);
    setIdentifierFilterValue('');
    setCategoryFilterValue([]);
    setSubcategoryFilterValue([]);
    setDurationFilterValue(['', '']);
    setPointFilterValue(['', '']);
    setMaxSlienceFilterValue(['', '']);
    setTypeFilterValue(null);
    setSpecialFilterFilterValue([]);
    setLanguageFilterValue(null);
    setContactTypeFilterValue([]);
    setIssueStatusFilterValue(null);
    setSentimentFilterValue([]);
    setIsAnalysisCompletedFilterValue(null);
    setKeywordsFilterValue([]);
    setVendorFilterValue(null);
    setFilters([]);
  };

  useEffect(() => {
    fetchDashboardData();
    if (tenantTableRef && tenantTableRef.current) {
      tenantTableRef.current.setColumnFilters(filters);
    }
  }, [filters]);

  const columns = getChannelTableColumns(user, channelType, getTenantParameters(channelType), getAgents(channelType), label);

  const chartJsOptions = getChartJsOptions({
    channelType,
    handleDateClick,
    label,
  });

  return (
    <>
      <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 10 }}>
        <FilterSection
          user={user}
          channelType={channelType}
          clearFilters={clearFilters}
          setFilter={setFilter}
          initDateType={initDateType}
          providerFilterValue={providerFilterValue}
          setProviderFilterValue={setProviderFilterValue}
          dateFilterValue={dateFilterValue}
          setDateFilterValue={setDateFilterValue}
          isAnalysisCompletedFilterValue={isAnalysisCompletedFilterValue}
          setIsAnalysisCompletedFilterValue={setIsAnalysisCompletedFilterValue}
          agentIdFilterValue={agentIdFilterValue}
          setAgentIdFilterValue={setAgentIdFilterValue}
          agents={getAgents(channelType)}
          detailedFiltersVisible={detailedFiltersVisible}
          setDetailedFiltersVisible={setDetailedFiltersVisible}
          alarmCategoriesFilterValue={alarmCategoriesFilterValue}
          setAlarmCategoriesFilterValue={setAlarmCategoriesFilterValue}
          identifierFilterValue={identifierFilterValue}
          setIdentifierFilterValue={setIdentifierFilterValue}
          categoryFilterValue={categoryFilterValue}
          setCategoryFilterValue={setCategoryFilterValue}
          subcategoryFilterValue={subcategoryFilterValue}
          setSubcategoryFilterValue={setSubcategoryFilterValue}
          pointFilterValue={pointFilterValue}
          setPointFilterValue={setPointFilterValue}
          durationFilterValue={durationFilterValue}
          setDurationFilterValue={setDurationFilterValue}
          maxSlienceFilterValue={maxSlienceFilterValue}
          setMaxSlienceFilterValue={setMaxSlienceFilterValue}
          typeFilterValue={typeFilterValue}
          setTypeFilterValue={setTypeFilterValue}
          analysisFilterValue={analysisFilterValue}
          setAnalysisFilterValue={setAnalysisFilterValue}
          specialFilterFilterValue={specialFilterFilterValue}
          setSpecialFilterFilterValue={setSpecialFilterFilterValue}
          languageFilterValue={languageFilterValue}
          setLanguageFilterValue={setLanguageFilterValue}
          contactTypeFilterValue={contactTypeFilterValue}
          setContactTypeFilterValue={setContactTypeFilterValue}
          issueStatusFilterValue={issueStatusFilterValue}
          setIssueStatusFilterValue={setIssueStatusFilterValue}
          sentimentFilterValue={sentimentFilterValue}
          setSentimentFilterValue={setSentimentFilterValue}
          keywordsFilterValue={keywordsFilterValue}
          setKeywordsFilterValue={setKeywordsFilterValue}
          vendorFilterValue={vendorFilterValue}
          setVendorFilterValue={setVendorFilterValue}
          tenantParameters={getTenantParameters(channelType)}
        />
      </Card>
      {dashboardData && (
        <Grid mt="md">
          <Grid.Col span={{ md: 2 }}>
            <AverageQualityScore
              setFilter={setFilter}
              setPointFilterValue={setPointFilterValue}
              dashboardData={dashboardData}
              tenantParameters={getTenantParameters(channelType)}
              label={label}
              classes={classes}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 2 }}>
            <CallNumber
              setFilter={setFilter}
              setIsAnalysisCompletedFilterValue={setIsAnalysisCompletedFilterValue}
              dashboardData={dashboardData}
              channelType={channelType}
              label={label}
              classes={classes}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 2 }}>
            <AverageSilenceDuration
              setFilter={setFilter}
              setMaxSlienceFilterValue={setMaxSlienceFilterValue}
              dashboardData={dashboardData}
              tenantParameters={getTenantParameters(channelType)}
              label={label}
              classes={classes}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 2 }}>
            <ProhibitedWord
              setFilter={setFilter}
              setSpecialFilterFilterValue={setSpecialFilterFilterValue}
              dashboardData={dashboardData}
              label={label}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 4 }}>
            <ViolationStats
              setFilter={setFilter}
              setSpecialFilterFilterValue={setSpecialFilterFilterValue}
              dashboardData={dashboardData}
              label={label}
            />
          </Grid.Col>
          {/* <Grid.Col span={{ md: 3 }}>
            <AverageDuration
              setFilter={setFilter}
              setDurationFilterValue={setDurationFilterValue}
              dashboardData={dashboardData}
              tenantParameters={tenantParameters}
              label={label}
              classes={classes}
            />
          </Grid.Col> */}
          {/* <Grid.Col span={12}>
            <SimpleGrid cols={{ base: 1, xs: 2, sm: 3, md: 3 }}> */}
          {/* <ViolationStats
                setFilter={setFilter}
                setSpecialFilterFilterValue={setSpecialFilterFilterValue}
                dashboardData={dashboardData}
                label={label}
              /> */}
          {/* <ProhibitedWord
                setFilter={setFilter}
                setSpecialFilterFilterValue={setSpecialFilterFilterValue}
                dashboardData={dashboardData}
                label={label}
              /> */}
          {/* </SimpleGrid>
          </Grid.Col> */}
          <Grid.Col span={{ md: 12 }}>
            <LineGraph dashboardData={dashboardData} chartJsOptions={chartJsOptions} label={label} />
          </Grid.Col>
          <Grid.Col span={{ md: 6 }}>
            <NumberOfMainCategories
              setFilter={setFilter}
              setCategoryFilterValue={setCategoryFilterValue}
              setSubcategoryFilterValue={setSubcategoryFilterValue}
              dashboardData={dashboardData}
              tenantParameters={getTenantParameters(channelType)}
              label={label}
              classes={classes}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 6 }}>
            <QualityControlNumber
              setFilter={setFilter}
              setAnalysisFilterValue={setAnalysisFilterValue}
              dashboardData={dashboardData}
              label={label}
              classes={classes}
            />
          </Grid.Col>
        </Grid>
      )}
      <ChannelTable
        initColumnFilters={filters}
        columns={columns}
        tenantTableRef={tenantTableRef}
        channelType={channelType}
        label={label}
        user={user}
        agents={getAgents(channelType)}
        permissions={permissions}
        tenantParameters={getTenantParameters(channelType)}
        fetchAuthClient={fetchAuthClient}
        selectedItemActionButtons={[]}
      />
    </>
  );
}
